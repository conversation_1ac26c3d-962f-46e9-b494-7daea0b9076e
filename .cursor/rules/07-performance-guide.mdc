---
description: 
globs: 
alwaysApply: true
---
# 性能优化指南

## 代码分割与懒加载

### 路由懒加载
```typescript
// 路由懒加载
import { lazy, Suspense } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import LoadingSpinner from './components/LoadingSpinner';

const Dashboard = lazy(() => import('./pages/Dashboard'));
const Reports = lazy(() => import('./pages/Reports'));
const Settings = lazy(() => import('./pages/Settings'));

const App = () => (
  <BrowserRouter>
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/reports" element={<Reports />} />
        <Route path="/settings" element={<Settings />} />
      </Routes>
    </Suspense>
  </BrowserRouter>
);
```

### 组件懒加载
```typescript
// 组件懒加载
const LazyChart = lazy(() => import('./components/Chart'));

const Dashboard = () => {
  const [showChart, setShowChart] = useState(false);
  
  return (
    <div>
      <button onClick={() => setShowChart(true)}>
        显示图表
      </button>
      
      {showChart && (
        <Suspense fallback={<LoadingSpinner />}>
          <LazyChart data={chartData} />
        </Suspense>
      )}
    </div>
  );
};
```

## 渲染优化

### React.memo 使用
```typescript
// 使用 React.memo 避免不必要的重渲染
const ExpensiveComponent = React.memo<Props>(({ data, onAction }) => {
  const processedData = useMemo(() => {
    return data.map(item => expensiveCalculation(item));
  }, [data]);

  return (
    <div>
      {processedData.map(item => (
        <Item key={item.id} data={item} onClick={onAction} />
      ))}
    </div>
  );
});

// 自定义比较函数
const areEqual = (prevProps: Props, nextProps: Props) => {
  return (
    prevProps.data.length === nextProps.data.length &&
    prevProps.data.every((item, index) => item.id === nextProps.data[index].id)
  );
};

const OptimizedComponent = React.memo(ExpensiveComponent, areEqual);
```

### 虚拟列表
```typescript
// hooks/useVirtualList.ts
const useVirtualList = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleStartIndex = Math.floor(scrollTop / itemHeight);
  const visibleEndIndex = Math.min(
    visibleStartIndex + Math.ceil(containerHeight / itemHeight),
    items.length - 1
  );
  
  const visibleItems = items.slice(visibleStartIndex, visibleEndIndex + 1);
  
  const totalHeight = items.length * itemHeight;
  const offsetY = visibleStartIndex * itemHeight;
  
  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop,
  };
};
```

## 防抖与节流

### 防抖函数
```typescript
// hooks/useDebounce.ts
const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};
```

### 节流函数
```typescript
// hooks/useThrottle.ts
const useThrottle = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): T => {
  const { current } = useRef<{
    fn: T;
    timer: NodeJS.Timeout | null;
  }>({
    fn,
    timer: null,
  });

  current.fn = fn;

  return useCallback(
    ((...args: any[]) => {
      if (!current.timer) {
        current.timer = setTimeout(() => {
          current.timer = null;
        }, delay);
        current.fn(...args);
      }
    }) as T,
    [delay]
  );
};
```

## 缓存策略

### 数据缓存
```typescript
// hooks/useCache.ts
const useCache = <T>(
  key: string,
  fetcher: () => Promise<T>,
  options: {
    ttl?: number; // 缓存时间 (毫秒)
    staleWhileRevalidate?: boolean;
  } = {}
) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const { ttl = 5 * 60 * 1000, staleWhileRevalidate = true } = options;

  useEffect(() => {
    const cachedData = localStorage.getItem(key);
    const cachedTimestamp = localStorage.getItem(`${key}_timestamp`);
    
    const now = Date.now();
    const isStale = !cachedTimestamp || (now - parseInt(cachedTimestamp)) > ttl;

    if (cachedData && !isStale) {
      setData(JSON.parse(cachedData));
      return;
    }

    if (cachedData && staleWhileRevalidate) {
      setData(JSON.parse(cachedData));
    }

    setLoading(true);
    fetcher()
      .then((result) => {
        setData(result);
        localStorage.setItem(key, JSON.stringify(result));
        localStorage.setItem(`${key}_timestamp`, now.toString());
      })
      .catch(setError)
      .finally(() => setLoading(false));
  }, [key, fetcher, ttl, staleWhileRevalidate]);

  return { data, loading, error };
};
```

### 计算结果缓存
```typescript
const useMemoizedValue = <T>(
  factory: () => T,
  deps: React.DependencyList
): T => {
  return useMemo(() => factory(), deps);
};
```
