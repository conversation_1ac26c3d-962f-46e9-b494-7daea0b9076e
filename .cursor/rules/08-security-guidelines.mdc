---
description: 
globs: 
alwaysApply: false
---
# 安全开发规范

## XSS 防护

### HTML 内容清理
```typescript
// utils/sanitizer.ts
import DOMPurify from 'dompurify';

/**
 * 清理 HTML 内容，防止 XSS 攻击
 */
export const sanitizeHtml = (html: string): string => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'u', 'strong', 'em', 'p', 'br'],
    ALLOWED_ATTR: [],
  });
};

/**
 * 转义用户输入
 */
export const escapeHtml = (text: string): string => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};
```

### 安全的富文本渲染
```typescript
// 安全的富文本渲染组件
const SafeHtml: React.FC<{ html: string }> = ({ html }) => {
  const sanitizedHtml = useMemo(() => sanitizeHtml(html), [html]);
  
  return (
    <div
      dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
    />
  );
};
```

## CSRF 防护

### HTTP 客户端配置
```typescript
// services/httpClient.ts
import axios from 'axios';

// 创建 axios 实例
const httpClient = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL,
  timeout: 10000,
  withCredentials: true, // 包含 cookies
});

// 请求拦截器 - 添加 CSRF Token
httpClient.interceptors.request.use(
  (config) => {
    // 从 meta 标签或 cookie 中获取 CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     getCookie('csrftoken');
    
    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// 获取 Cookie 值
const getCookie = (name: string): string | null => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
};
```

## 敏感信息处理

### 数据加密
```typescript
// utils/encryption.ts
import CryptoJS from 'crypto-js';

const SECRET_KEY = process.env.REACT_APP_ENCRYPTION_KEY || 'default-key';

/**
 * 加密敏感数据
 */
export const encrypt = (data: string): string => {
  return CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
};

/**
 * 解密数据
 */
export const decrypt = (encryptedData: string): string => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};
```

### 安全的本地存储
```typescript
export const secureStorage = {
  setItem: (key: string, value: string) => {
    const encryptedValue = encrypt(value);
    localStorage.setItem(key, encryptedValue);
  },
  
  getItem: (key: string): string | null => {
    const encryptedValue = localStorage.getItem(key);
    if (!encryptedValue) return null;
    
    try {
      return decrypt(encryptedValue);
    } catch {
      // 解密失败，可能是旧数据
      localStorage.removeItem(key);
      return null;
    }
  },
  
  removeItem: (key: string) => {
    localStorage.removeItem(key);
  },
};
```

### 敏感数据脱敏
```typescript
export const maskSensitiveData = {
  phone: (phone: string): string => {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  },
  
  email: (email: string): string => {
    const [username, domain] = email.split('@');
    const maskedUsername = username.length > 2 
      ? `${username[0]}***${username[username.length - 1]}`
      : username;
    return `${maskedUsername}@${domain}`;
  },
  
  idCard: (idCard: string): string => {
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
  },
};
```

## 权限控制

### 权限管理 Hook
```typescript
// hooks/usePermissions.ts
enum Permission {
  READ_USERS = 'read:users',
  WRITE_USERS = 'write:users',
  DELETE_USERS = 'delete:users',
  READ_REPORTS = 'read:reports',
  WRITE_REPORTS = 'write:reports',
}

interface User {
  id: string;
  role: string;
  permissions: Permission[];
}

const usePermissions = () => {
  const { user } = useAuth();

  const hasPermission = useCallback(
    (permission: Permission): boolean => {
      return user?.permissions?.includes(permission) || false;
    },
    [user]
  );

  const hasAnyPermission = useCallback(
    (permissions: Permission[]): boolean => {
      return permissions.some(permission => hasPermission(permission));
    },
    [hasPermission]
  );

  const hasAllPermissions = useCallback(
    (permissions: Permission[]): boolean => {
      return permissions.every(permission => hasPermission(permission));
    },
    [hasPermission]
  );

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  };
};
```

### 权限控制组件
```typescript
// 权限控制组件
const ProtectedComponent: React.FC<{
  permission: Permission;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}> = ({ permission, fallback = null, children }) => {
  const { hasPermission } = usePermissions();

  if (!hasPermission(permission)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};
```
