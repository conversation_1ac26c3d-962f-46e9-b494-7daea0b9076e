---
type: "always_apply"
---

# 组件开发规范

## 组件文件结构
```
ComponentName/
├── index.tsx          # 主组件文件
├── ComponentName.tsx  # 组件实现 (复杂组件)
├── styles.ts          # 样式文件
├── types.ts           # 类型定义
├── hooks.ts           # 组件专用 hooks (可选)
├── utils.ts           # 组件工具函数 (可选)
├── __tests__/         # 测试文件
│   ├── ComponentName.test.tsx
│   └── __snapshots__/
└── README.md          # 组件文档
```


## 组件设计原则

### 单一职责原则
- 每个组件只负责一项功能
- 复杂功能拆分为多个子组件
- UI 渲染与业务逻辑分离

### 可组合性设计
- 使用组合优于继承
- 实现复合组件模式
- 使用 Props 传递配置项
- 使用 children 实现内容定制
