---
type: "always_apply"
---

# 代码规范

## TypeScript 规范

### 配置要求
- strict: true
- noImplicitAny: true
- noImplicitReturns: true
- noUnusedLocals: true
- noUnusedParameters: true

### 类型定义规范
- 优先使用 interface 定义对象结构
- 复杂联合类型使用 type
- 禁止使用 any，使用 unknown 或具体类型
- 使用类型守卫进行类型收窄

## React 规范

### 组件定义
- 使用函数组件 + TypeScript
- 明确定义 Props 接口
- 默认使用 React.FC 类型
- 对于复杂组件使用 React.memo 优化

### Hooks 使用规范
- 自定义 Hook 命名使用 use 前缀
- 使用 useCallback 优化回调函数
- 使用 useMemo 缓存复杂计算结果
- 遵循 React Hooks 依赖项规则

## 命名规范

- 组件文件: PascalCase (UserProfile.tsx)
- 组件目录: PascalCase (UserProfile/)
- Hook 文件: camelCase 且 use 前缀 (useAuth.ts)
- 工具函数: camelCase (formatDate.ts)
- 常量: UPPER_SNAKE_CASE (API_BASE_URL)
- 接口/类型: PascalCase (UserData, ApiResponse)

## 代码注释规范

- 使用 JSDoc 风格注释
- 组件、Hook、函数需要添加描述注释
- 复杂逻辑需要添加行内注释
- 使用 TODO/FIXME 标记待完成项
- 示例代码应当添加在 @example 标签下
