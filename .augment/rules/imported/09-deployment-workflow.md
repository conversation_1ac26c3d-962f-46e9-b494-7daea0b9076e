---
type: "agent_requested"
---

# 部署与发布流程

## Git 分支管理策略

### 分支结构
```
main           # 生产环境分支
├── develop    # 开发环境分支
├── feature/*  # 功能分支
├── hotfix/*   # 热修复分支
└── release/*  # 发布分支
```

### 分支命名规范
- `feature/[username]/[feature-name]` - 功能开发
- `bugfix/[username]/[bug-description]` - 问题修复
- `hotfix/[version]-[issue-description]` - 紧急修复
- `release/[version]` - 版本发布

### Commit 消息规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 提交类型
- `feat`: 新功能
- `fix`: 问题修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变动

## CI/CD 配置

### GitHub Actions 示例
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linter
      run: npm run lint
    
    - name: Run type check
      run: npm run type-check
    
    - name: Run tests
      run: npm run test:coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
      env:
        REACT_APP_API_BASE_URL: ${{ secrets.API_BASE_URL }}
        REACT_APP_SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-files
        path: build/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-files
        path: build/
    
    - name: Deploy to production
      run: |
        # 部署逻辑 (AWS S3, Netlify, Vercel 等)
        echo "Deploying to production..."
```

## 版本管理

### package.json 配置
```json
{
  "name": "finsight-frontend",
  "version": "1.2.3",
  "scripts": {
    "version:patch": "npm version patch && git push --follow-tags",
    "version:minor": "npm version minor && git push --follow-tags",
    "version:major": "npm version major && git push --follow-tags",
    "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s",
    "release": "npm run build && npm run changelog && git add CHANGELOG.md && git commit -m 'docs: update changelog'"
  }
}
```

### 自动化版本发布脚本
```javascript
// scripts/release.js
const { execSync } = require('child_process');
const fs = require('fs');

const releaseType = process.argv[2] || 'patch';
const validTypes = ['patch', 'minor', 'major'];

if (!validTypes.includes(releaseType)) {
  console.error('Invalid release type. Use patch, minor, or major.');
  process.exit(1);
}

try {
  // 1. 运行测试
  console.log('Running tests...');
  execSync('npm test', { stdio: 'inherit' });

  // 2. 构建项目
  console.log('Building project...');
  execSync('npm run build', { stdio: 'inherit' });

  // 3. 更新版本号
  console.log(`Bumping ${releaseType} version...`);
  execSync(`npm version ${releaseType}`, { stdio: 'inherit' });

  // 4. 生成 changelog
  console.log('Generating changelog...');
  execSync('npm run changelog', { stdio: 'inherit' });

  // 5. 推送到远程仓库
  console.log('Pushing to remote...');
  execSync('git push --follow-tags', { stdio: 'inherit' });

  console.log('Release completed successfully!');
} catch (error) {
  console.error('Release failed:', error.message);
  process.exit(1);
}
```

## 环境配置

### 环境变量管理
```typescript
// config/environment.ts
interface EnvironmentConfig {
  API_BASE_URL: string;
  APP_ENV: 'development' | 'staging' | 'production';
  SENTRY_DSN?: string;
  ANALYTICS_ID?: string;
  FEATURE_FLAGS: {
    enableNewDashboard: boolean;
    enableAdvancedReports: boolean;
  };
}

const getEnvironmentConfig = (): EnvironmentConfig => {
  const env = process.env.NODE_ENV || 'development';
  
  const baseConfig = {
    API_BASE_URL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001',
    APP_ENV: env as EnvironmentConfig['APP_ENV'],
    SENTRY_DSN: process.env.REACT_APP_SENTRY_DSN,
    ANALYTICS_ID: process.env.REACT_APP_ANALYTICS_ID,
  };

  const featureFlags: EnvironmentConfig['FEATURE_FLAGS'] = {
    enableNewDashboard: process.env.REACT_APP_ENABLE_NEW_DASHBOARD === 'true',
    enableAdvancedReports: process.env.REACT_APP_ENABLE_ADVANCED_REPORTS === 'true',
  };

  return {
    ...baseConfig,
    FEATURE_FLAGS: featureFlags,
  };
};

export const config = getEnvironmentConfig();
```

### Docker 配置
```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=builder /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

## 监控与日志

### 错误监控 (Sentry)
```typescript
// utils/errorReporting.ts
import * as Sentry from '@sentry/react';
import { BrowserTracing } from '@sentry/tracing';

export const initializeErrorReporting = () => {
  if (process.env.NODE_ENV === 'production' && process.env.REACT_APP_SENTRY_DSN) {
    Sentry.init({
      dsn: process.env.REACT_APP_SENTRY_DSN,
      integrations: [
        new BrowserTracing(),
      ],
      tracesSampleRate: 0.1,
      environment: process.env.NODE_ENV,
      beforeSend(event) {
        // 过滤敏感信息
        if (event.user?.email) {
          event.user.email = maskSensitiveData.email(event.user.email);
        }
        return event;
      },
    });
  }
};
```

### 性能监控
```typescript
// utils/analytics.ts
interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  userId?: string;
}

class Analytics {
  private initialized = false;

  initialize() {
    if (this.initialized || process.env.NODE_ENV !== 'production') {
      return;
    }

    // 初始化分析工具 (Google Analytics, Mixpanel 等)
    this.initialized = true;
  }

  track(event: AnalyticsEvent) {
    if (!this.initialized) return;

    console.log('Analytics Event:', event);
    // 发送事件到分析平台
  }

  trackPageView(page: string) {
    this.track({
      name: 'page_view',
      properties: { page },
    });
  }

  trackPerformance(metric: string, value: number) {
    this.track({
      name: 'performance_metric',
      properties: { metric, value },
    });
  }
}

export const analytics = new Analytics();
```
