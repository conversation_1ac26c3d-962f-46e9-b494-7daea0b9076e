{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "src", "paths": {"@/*": ["*"], "@components/*": ["components/*"], "@pages/*": ["pages/*"], "@routes/*": ["routes/*"], "@layouts/*": ["layouts/*"], "@utils/*": ["utils/*"], "@constants/*": ["constants/*"], "@hooks/*": ["hooks/*"], "@store/*": ["store/*"], "@services/*": ["services/*"], "@types/*": ["types/*"], "@styles/*": ["styles/*"], "@assets/*": ["assets/*"], "@features/*": ["features/*"]}}, "include": ["src", "src/**/*"], "exclude": ["node_modules", "build", "dist"]}