#!/bin/bash

# 环境配置文件加密脚本
# 用于创建加密的环境配置文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
  echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示使用说明
show_usage() {
  echo "使用方法: $0 <environment> [encryption_key]"
  echo ""
  echo "参数:"
  echo "  environment     环境名称 (development 或 production)"
  echo "  encryption_key  加密密钥 (可选，未提供时自动生成)"
  echo ""
  echo "示例:"
  echo "  $0 development"
  echo "  $0 production"
  echo "  $0 development 'your-custom-key'"
  echo ""
  echo "环境变量:"
  echo "  AUTO_GENERATE_KEY=true  自动生成并显示密钥"
}

# 生成随机密钥
generate_encryption_key() {
  openssl rand -base64 32
}

# 创建环境配置模板
create_env_template() {
  local env_name="$1"
  local template_file=".env.${env_name}.template"
  
  log_step "创建 $env_name 环境配置模板"
  
  case "$env_name" in
    "development")
      cat > "$template_file" << EOF
# 开发环境配置
REACT_APP_ENV=development
REACT_APP_API_BASE_URL=https://api-dev.example.com
REACT_APP_DEBUG=true
REACT_APP_VERSION=1.0.0-dev
REACT_APP_LOG_LEVEL=debug

# 数据库配置 (开发环境)
REACT_APP_DB_HOST=localhost
REACT_APP_DB_PORT=5432
REACT_APP_DB_NAME=finsight_dev

# 认证配置
REACT_APP_AUTH_DOMAIN=auth-dev.example.com
REACT_APP_AUTH_CLIENT_ID=dev_client_id
REACT_APP_AUTH_REDIRECT_URI=http://localhost:3000/callback

# 第三方服务配置
REACT_APP_ANALYTICS_ID=dev_analytics_id
REACT_APP_SENTRY_DSN=https://dev-sentry.example.com

# 功能开关
REACT_APP_ENABLE_FEATURE_X=true
REACT_APP_ENABLE_MOCK_DATA=true
EOF
      ;;
    "production")
      cat > "$template_file" << EOF
# 生产环境配置
REACT_APP_ENV=production
REACT_APP_API_BASE_URL=https://api.example.com
REACT_APP_DEBUG=false
REACT_APP_VERSION=1.0.0
REACT_APP_LOG_LEVEL=error

# 数据库配置 (生产环境)
REACT_APP_DB_HOST=prod-db.example.com
REACT_APP_DB_PORT=5432
REACT_APP_DB_NAME=finsight_prod

# 认证配置
REACT_APP_AUTH_DOMAIN=auth.example.com
REACT_APP_AUTH_CLIENT_ID=prod_client_id
REACT_APP_AUTH_REDIRECT_URI=https://app.example.com/callback

# 第三方服务配置
REACT_APP_ANALYTICS_ID=prod_analytics_id
REACT_APP_SENTRY_DSN=https://sentry.example.com

# 功能开关
REACT_APP_ENABLE_FEATURE_X=true
REACT_APP_ENABLE_MOCK_DATA=false
EOF
      ;;
    *)
      log_error "未知环境: $env_name"
      return 1
      ;;
  esac
  
  log_info "✅ 模板文件已创建: $template_file"
  log_warn "请编辑模板文件，填入实际的配置值，然后重新运行此脚本"
}

# 加密环境配置文件
encrypt_env_file() {
  local env_name="$1"
  local encryption_key="$2"
  local source_file=".env.${env_name}"
  local encrypted_file=".env.${env_name}.enc"
  
  log_step "加密 $env_name 环境配置文件"
  
  # 检查源文件是否存在
  if [ ! -f "$source_file" ]; then
    log_error "配置文件不存在: $source_file"
    create_env_template "$env_name"
    return 1
  fi
  
  # 检查源文件是否为空
  if [ ! -s "$source_file" ]; then
    log_error "配置文件为空: $source_file"
    return 1
  fi
  
  # 检查是否包含有效配置
  local config_count=$(grep -c "^[^#].*=" "$source_file" 2>/dev/null || echo 0)
  if [ "$config_count" -eq 0 ]; then
    log_error "配置文件无有效配置项: $source_file"
    return 1
  fi
  
  log_info "源文件: $source_file (配置项: $config_count)"
  log_info "目标文件: $encrypted_file"
  log_info "密钥长度: ${#encryption_key} 字符"
  
  # 执行加密
  if openssl aes-256-cbc -a -pbkdf2 -iter 100000 \
     -in "$source_file" \
     -out "$encrypted_file" \
     -k "$encryption_key"; then
    
    log_info "✅ 加密成功"
    log_info "加密文件大小: $(wc -c < "$encrypted_file") 字节"
    
    # 验证加密文件
    if [ -s "$encrypted_file" ]; then
      log_info "✅ 加密文件验证通过"
      
      # 询问是否删除原始文件
      if [ "${AUTO_CLEANUP:-false}" = "true" ]; then
        rm -f "$source_file"
        log_warn "🗑️ 原始文件已删除: $source_file"
      else
        log_warn "⚠️ 请手动删除原始文件以确保安全: rm $source_file"
      fi
      
      return 0
    else
      log_error "加密文件为空，加密可能失败"
      return 1
    fi
  else
    log_error "加密失败"
    return 1
  fi
}

# 测试解密
test_decryption() {
  local env_name="$1"
  local encryption_key="$2"
  local encrypted_file=".env.${env_name}.enc"
  local test_output="/tmp/test_decrypt_$$"
  
  log_step "测试解密 $env_name 环境配置"
  
  if [ ! -f "$encrypted_file" ]; then
    log_error "加密文件不存在: $encrypted_file"
    return 1
  fi
  
  # 尝试解密
  if openssl aes-256-cbc -d -a -pbkdf2 -iter 100000 \
     -in "$encrypted_file" \
     -out "$test_output" \
     -k "$encryption_key" 2>/dev/null; then
    
    # 验证解密结果
    if [ -s "$test_output" ] && grep -q "=" "$test_output" 2>/dev/null; then
      local config_count=$(grep -c "^[^#].*=" "$test_output" 2>/dev/null || echo 0)
      log_info "✅ 解密测试成功 (配置项: $config_count)"
      
      # 显示配置结构
      log_info "配置项预览:"
      grep "^[^#].*=" "$test_output" | cut -d'=' -f1 | head -5 | while read key; do
        echo "  - $key"
      done
      
      rm -f "$test_output"
      return 0
    else
      log_error "解密结果无效"
      rm -f "$test_output"
      return 1
    fi
  else
    log_error "解密测试失败"
    rm -f "$test_output"
    return 1
  fi
}

# 主函数
main() {
  local env_name="$1"
  local encryption_key="$2"
  
  # 检查参数
  if [ -z "$env_name" ]; then
    show_usage
    exit 1
  fi
  
  # 验证环境名称
  if [ "$env_name" != "development" ] && [ "$env_name" != "production" ]; then
    log_error "无效的环境名称: $env_name"
    log_error "支持的环境: development, production"
    exit 1
  fi
  
  log_info "🚀 开始处理 $env_name 环境配置"
  
  # 生成或使用提供的加密密钥
  if [ -z "$encryption_key" ]; then
    encryption_key=$(generate_encryption_key)
    log_info "✨ 已生成新的加密密钥"
  else
    log_info "📝 使用提供的加密密钥"
  fi
  
  # 显示密钥信息
  echo ""
  log_step "重要信息 - 请妥善保存以下密钥"
  echo "======================================"
  echo "环境: $env_name"
  echo "密钥: $encryption_key"
  echo "GitHub Secret 名称: $([ "$env_name" = "development" ] && echo "DEV_ENCRYPTION_KEY" || echo "PROD_ENCRYPTION_KEY")"
  echo "======================================"
  echo ""
  
  # 执行加密
  if encrypt_env_file "$env_name" "$encryption_key"; then
    # 测试解密
    if test_decryption "$env_name" "$encryption_key"; then
      log_info "🎉 $env_name 环境配置处理完成"
      echo ""
      log_step "下一步操作:"
      echo "1. 将加密密钥添加到 GitHub Secrets"
      echo "2. 提交加密文件到代码仓库: git add .env.${env_name}.enc"
      echo "3. 确保原始配置文件已删除或添加到 .gitignore"
    else
      log_error "解密测试失败，请检查加密过程"
      exit 1
    fi
  else
    log_error "加密失败"
    exit 1
  fi
}

# 执行主函数
main "$@" 