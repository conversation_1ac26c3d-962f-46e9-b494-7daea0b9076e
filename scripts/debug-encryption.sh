#!/bin/bash

# 加解密调试脚本
# 用于测试和诊断加解密问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
  echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 测试加解密
test_encryption_decryption() {
  local test_key="$1"
  local test_content="$2"
  local test_file="/tmp/test_env_$$"
  local enc_file="/tmp/test_env_$$.enc"
  local dec_file="/tmp/test_env_$$.dec"
  
  log_info "开始加解密测试"
  log_debug "密钥长度: ${#test_key} 字符"
  log_debug "内容长度: ${#test_content} 字符"
  
  # 创建测试文件
  echo "$test_content" > "$test_file"
  
  # 测试加密
  log_info "测试加密..."
  if openssl aes-256-cbc -a -pbkdf2 -iter 100000 -in "$test_file" -out "$enc_file" -k "$test_key"; then
    log_info "✅ 加密成功"
    log_debug "加密文件大小: $(wc -c < "$enc_file") 字节"
    log_debug "加密内容: $(head -c 64 "$enc_file")"
  else
    log_error "❌ 加密失败"
    cleanup_test_files "$test_file" "$enc_file" "$dec_file"
    return 1
  fi
  
  # 测试解密
  log_info "测试解密..."
  local decrypt_methods=(
    "openssl aes-256-cbc -d -a -pbkdf2 -iter 100000 -in '$enc_file' -out '$dec_file' -k '$test_key'"
    "openssl aes-256-cbc -d -a -pbkdf2 -iter 100000 -in '$enc_file' -out '$dec_file' -pass pass:'$test_key'"
    "openssl aes-256-cbc -d -a -in '$enc_file' -out '$dec_file' -k '$test_key'"
    "openssl aes-256-cbc -d -a -in '$enc_file' -out '$dec_file' -pass pass:'$test_key'"
  )
  
  for method in "${decrypt_methods[@]}"; do
    log_debug "尝试方法: $method"
    
    if eval "$method" 2>/dev/null; then
      if [ -s "$dec_file" ]; then
        local original_hash=$(md5sum "$test_file" | cut -d' ' -f1)
        local decrypted_hash=$(md5sum "$dec_file" | cut -d' ' -f1)
        
        if [ "$original_hash" = "$decrypted_hash" ]; then
          log_info "✅ 解密成功且内容一致"
          log_debug "原始内容: $(cat "$test_file")"
          log_debug "解密内容: $(cat "$dec_file")"
          cleanup_test_files "$test_file" "$enc_file" "$dec_file"
          return 0
        else
          log_warn "解密成功但内容不一致"
        fi
      else
        log_warn "解密文件为空"
      fi
    else
      log_warn "解密方法失败"
    fi
    
    # 清理解密文件准备下一次尝试
    rm -f "$dec_file"
  done
  
  log_error "❌ 所有解密方法均失败"
  cleanup_test_files "$test_file" "$enc_file" "$dec_file"
  return 1
}

# 清理测试文件
cleanup_test_files() {
  local files=("$@")
  for file in "${files[@]}"; do
    [ -f "$file" ] && rm -f "$file"
  done
}

# 检查现有文件
check_existing_files() {
  log_info "检查现有环境文件"
  
  local files=(".env.development" ".env.development.enc" ".env.production" ".env.production.enc")
  
  for file in "${files[@]}"; do
    if [ -f "$file" ]; then
      log_info "✅ 发现文件: $file"
      log_debug "  大小: $(wc -c < "$file") 字节"
      if [[ "$file" == *.enc ]]; then
        log_debug "  前64字符: $(head -c 64 "$file")"
      else
        log_debug "  行数: $(wc -l < "$file")"
        log_debug "  配置项: $(grep -c "=" "$file" 2>/dev/null || echo 0)"
      fi
    else
      log_warn "❌ 未找到文件: $file"
    fi
  done
}

# 检查环境变量
check_environment() {
  log_info "检查环境变量"
  
  local vars=("DEV_ENCRYPTION_KEY" "PROD_ENCRYPTION_KEY" "MATRIX_ENV")
  
  for var in "${vars[@]}"; do
    local value="${!var}"
    if [ -n "$value" ]; then
      log_info "✅ $var: 已设置 (长度: ${#value})"
    else
      log_warn "❌ $var: 未设置或为空"
    fi
  done
}

# 测试真实文件解密
test_real_file_decryption() {
  local env_name="${1:-development}"
  local encryption_key=""
  
  case "$env_name" in
    "development")
      encryption_key="$DEV_ENCRYPTION_KEY"
      ;;
    "production")
      encryption_key="$PROD_ENCRYPTION_KEY"
      ;;
    *)
      log_error "未知环境: $env_name"
      return 1
      ;;
  esac
  
  local enc_file=".env.${env_name}.enc"
  
  if [ -z "$encryption_key" ]; then
    log_error "环境 $env_name 的加密密钥为空"
    return 1
  fi
  
  if [ ! -f "$enc_file" ]; then
    log_error "加密文件 $enc_file 不存在"
    return 1
  fi
  
  log_info "测试真实文件解密: $enc_file"
  
  local test_output="/tmp/real_test_$$.env"
  local methods=(
    "openssl aes-256-cbc -d -a -pbkdf2 -iter 100000 -in '$enc_file' -out '$test_output' -k '$encryption_key'"
    "openssl aes-256-cbc -d -a -pbkdf2 -iter 100000 -in '$enc_file' -out '$test_output' -pass pass:'$encryption_key'"
  )
  
  for method in "${methods[@]}"; do
    log_debug "尝试方法: $method"
    
    if eval "$method" 2>/dev/null; then
      if [ -s "$test_output" ] && grep -q "=" "$test_output" 2>/dev/null; then
        log_info "✅ 真实文件解密成功"
        log_debug "解密后大小: $(wc -c < "$test_output") 字节"
        log_debug "配置项数量: $(grep -c "=" "$test_output")"
        rm -f "$test_output"
        return 0
      fi
    fi
    
    rm -f "$test_output"
  done
  
  log_error "❌ 真实文件解密失败"
  return 1
}

# 主函数
main() {
  local action="${1:-all}"
  
  log_info "开始加解密调试 - 操作: $action"
  
  case "$action" in
    "test")
      # 基础加解密测试
      test_encryption_decryption "test123" "TEST_VAR=hello_world"
      ;;
    "check")
      # 检查文件和环境
      check_environment
      check_existing_files
      ;;
    "real")
      # 测试真实文件
      local env="${2:-development}"
      test_real_file_decryption "$env"
      ;;
    "all")
      # 全面测试
      check_environment
      check_existing_files
      test_encryption_decryption "test123" "TEST_VAR=hello_world"
      
      if [ -n "$DEV_ENCRYPTION_KEY" ]; then
        test_real_file_decryption "development"
      fi
      
      if [ -n "$PROD_ENCRYPTION_KEY" ]; then
        test_real_file_decryption "production"
      fi
      ;;
    *)
      echo "用法: $0 [test|check|real|all] [environment]"
      echo "  test  - 基础加解密测试"
      echo "  check - 检查文件和环境变量"
      echo "  real  - 测试真实文件解密"
      echo "  all   - 全面测试（默认）"
      exit 1
      ;;
  esac
  
  log_info "调试完成"
}

# 执行主函数
main "$@" 