/**
 * 安全漏洞修复脚本
 * 用于处理 npm audit 发现的安全漏洞
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 执行命令并返回结果
 * @param {string} command - 要执行的命令
 * @param {boolean} suppressError - 是否忽略错误
 * @returns {string} 命令输出
 */
function executeCommand(command, suppressError = false) {
  try {
    const result = execSync(command, { 
      encoding: 'utf8', 
      stdio: ['inherit', 'pipe', 'pipe'] 
    });
    return result;
  } catch (error) {
    if (!suppressError) {
      console.error(`❌ 命令执行失败: ${command}`);
      console.error(error.message);
    }
    return null;
  }
}

/**
 * 检查和修复安全漏洞
 */
function fixSecurityVulnerabilities() {
  console.log('🔍 开始安全漏洞修复...\n');

  // 1. 首先检查当前的漏洞状态
  console.log('📊 检查当前安全漏洞状态:');
  const auditResult = executeCommand('npm audit --json', true);
  
  if (auditResult) {
    try {
      const audit = JSON.parse(auditResult);
      console.log(`发现 ${audit.metadata.vulnerabilities.total} 个漏洞:`);
      console.log(`  - 高危: ${audit.metadata.vulnerabilities.high}`);
      console.log(`  - 中危: ${audit.metadata.vulnerabilities.moderate}`);
      console.log(`  - 低危: ${audit.metadata.vulnerabilities.low}\n`);
    } catch (e) {
      console.log('无法解析审计结果\n');
    }
  }

  // 2. 创建package.json备份
  console.log('💾 创建 package.json 备份...');
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const backupPath = path.join(process.cwd(), 'package.json.backup');
  
  if (fs.existsSync(packageJsonPath)) {
    fs.copyFileSync(packageJsonPath, backupPath);
    console.log('✅ 备份已创建: package.json.backup\n');
  }

  // 3. 尝试自动修复
  console.log('🔧 尝试自动修复安全漏洞...');
  const fixResult = executeCommand('npm audit fix', true);
  
  if (fixResult) {
    console.log('✅ 自动修复完成\n');
  } else {
    console.log('⚠️ 自动修复未成功，尝试强制修复...\n');
  }

  // 4. 检查修复后的状态
  console.log('🔍 检查修复后的漏洞状态:');
  const postFixAudit = executeCommand('npm audit --json', true);
  
  if (postFixAudit) {
    try {
      const audit = JSON.parse(postFixAudit);
      const remaining = audit.metadata.vulnerabilities.total;
      
      if (remaining === 0) {
        console.log('🎉 所有安全漏洞已修复!\n');
        return true;
      } else {
        console.log(`⚠️ 仍有 ${remaining} 个漏洞未修复\n`);
        
        // 5. 处理剩余的高危漏洞
        if (audit.metadata.vulnerabilities.high > 0 || audit.metadata.vulnerabilities.critical > 0) {
          console.log('🚨 仍存在高危漏洞，尝试手动处理...');
          return handleHighRiskVulnerabilities();
        }
      }
    } catch (e) {
      console.log('⚠️ 无法解析修复后的审计结果\n');
    }
  }

  return false;
}

/**
 * 处理高危漏洞
 */
function handleHighRiskVulnerabilities() {
  console.log('\n🔧 处理特定的高危漏洞...');

  // 针对具体漏洞的处理方案
  const vulnerabilityFixes = [
    {
      name: 'nth-check',
      description: '修复 nth-check 正则表达式复杂度漏洞',
      action: () => {
        console.log('  - 更新 nth-check 到安全版本...');
        executeCommand('npm install nth-check@^2.1.1', true);
      }
    },
    {
      name: 'postcss',
      description: '修复 PostCSS 解析错误漏洞',
      action: () => {
        console.log('  - 更新 postcss 到安全版本...');
        executeCommand('npm install postcss@^8.4.31', true);
      }
    }
  ];

  vulnerabilityFixes.forEach(fix => {
    console.log(`🔧 ${fix.description}`);
    try {
      fix.action();
      console.log(`✅ ${fix.name} 修复完成`);
    } catch (error) {
      console.log(`❌ ${fix.name} 修复失败: ${error.message}`);
    }
  });

  // 重新检查
  console.log('\n🔍 重新检查安全状态...');
  const finalAudit = executeCommand('npm audit --json', true);
  
  if (finalAudit) {
    try {
      const audit = JSON.parse(finalAudit);
      const highRisk = audit.metadata.vulnerabilities.high + audit.metadata.vulnerabilities.critical;
      
      if (highRisk === 0) {
        console.log('✅ 高危漏洞已修复!');
        return true;
      } else {
        console.log(`⚠️ 仍有 ${highRisk} 个高危漏洞`);
      }
    } catch (e) {
      console.log('无法解析最终审计结果');
    }
  }

  return false;
}

/**
 * 生成安全报告
 */
function generateSecurityReport() {
  console.log('\n📋 生成安全报告...');
  
  const reportPath = path.join(process.cwd(), 'security-report.json');
  const auditResult = executeCommand('npm audit --json', true);
  
  if (auditResult) {
    fs.writeFileSync(reportPath, auditResult);
    console.log(`✅ 安全报告已生成: ${reportPath}`);
  }
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  const action = args[0] || 'fix';

  console.log('🛡️ 安全漏洞修复工具\n');

  switch (action) {
    case 'check':
      console.log('🔍 仅检查安全漏洞状态...');
      executeCommand('npm audit');
      break;
      
    case 'fix':
      const success = fixSecurityVulnerabilities();
      if (success) {
        console.log('🎉 安全修复完成!');
        process.exit(0);
      } else {
        console.log('❌ 安全修复未完全成功');
        process.exit(1);
      }
      break;
      
    case 'report':
      generateSecurityReport();
      break;
      
    default:
      console.log('使用方法:');
      console.log('  node security-fix.js check  - 检查漏洞');
      console.log('  node security-fix.js fix    - 修复漏洞');
      console.log('  node security-fix.js report - 生成报告');
      break;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  fixSecurityVulnerabilities,
  handleHighRiskVulnerabilities,
  generateSecurityReport
}; 