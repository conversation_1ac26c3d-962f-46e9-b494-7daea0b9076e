#!/bin/bash

# 环境配置解密脚本
# 用于GitHub Actions和本地开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# 解密函数
decrypt_env_file() {
  local env_name="$1"
  local encryption_key="$2"
  local enc_file=".env.${env_name}.enc"
  local output_file=".env.local"
  
  log_info "开始解密 $env_name 环境配置文件"
  
  # 检查密钥是否为空
  if [ -z "$encryption_key" ]; then
    log_error "加密密钥为空，无法解密"
    return 1
  fi
  
  # 检查加密文件是否存在
  if [ ! -f "$enc_file" ]; then
    log_error "未找到加密文件 $enc_file"
    return 1
  fi
  
  # 检查加密文件是否为空
  if [ ! -s "$enc_file" ]; then
    log_error "加密文件 $enc_file 为空"
    return 1
  fi
  
  log_info "加密文件: $enc_file"
  log_info "输出文件: $output_file"
  log_info "密钥长度: ${#encryption_key} 字符"
  
  # 显示加密文件的前几个字符进行调试
  log_info "加密文件前64字符: $(head -c 64 "$enc_file")"
  
  # 尝试多种解密方法
  local methods=(
    "openssl aes-256-cbc -d -a -pbkdf2 -iter 100000 -in '$enc_file' -out '$output_file' -k '$encryption_key'"
    "openssl aes-256-cbc -d -a -pbkdf2 -iter 100000 -in '$enc_file' -out '$output_file' -pass pass:'$encryption_key'"
    "openssl aes-256-cbc -d -a -in '$enc_file' -out '$output_file' -k '$encryption_key'"
    "openssl aes-256-cbc -d -a -in '$enc_file' -out '$output_file' -pass pass:'$encryption_key'"
  )
  
  for method in "${methods[@]}"; do
    log_info "尝试解密方法: $method"
    
    # 创建临时输出文件
    local temp_output="/tmp/env_temp_$$"
    
    # 替换输出文件路径
    local current_method="${method//$output_file/$temp_output}"
    
    if eval "$current_method" 2>/dev/null; then
      # 检查解密结果是否有效
      if [ -s "$temp_output" ] && grep -q "=" "$temp_output" 2>/dev/null; then
        cp "$temp_output" "$output_file"
        rm -f "$temp_output"
        log_info "✅ 解密成功！"
        log_info "解密后文件大小: $(wc -c < "$output_file") 字节"
        return 0
      else
        log_warn "解密结果无效，尝试下一种方法"
        rm -f "$temp_output"
      fi
    else
      log_warn "解密失败，尝试下一种方法"
      rm -f "$temp_output"
    fi
  done
  
  log_error "所有解密方法均失败"
  return 1
}

# 主函数
main() {
  # 使用正确的环境变量名
  local env_name="${ENVIRONMENT:-development}"
  local encryption_key=""
  
  log_info "目标环境: $env_name"
  
  # 根据环境选择密钥
  case "$env_name" in
    "development")
      encryption_key="$DEV_ENCRYPTION_KEY"
      log_info "使用开发环境加密密钥"
      ;;
    "production")
      encryption_key="$PROD_ENCRYPTION_KEY"
      log_info "使用生产环境加密密钥"
      ;;
    *)
      log_error "未知环境: $env_name，支持的环境: development, production"
      exit 1
      ;;
  esac
  
  # 检查密钥是否设置
  if [ -z "$encryption_key" ]; then
    log_error "${env_name} 环境的加密密钥未设置"
    log_error "请设置对应的环境变量:"
    case "$env_name" in
      "development")
        log_error "  - DEV_ENCRYPTION_KEY"
        ;;
      "production")
        log_error "  - PROD_ENCRYPTION_KEY"
        ;;
    esac
    exit 1
  fi
  
  # 尝试解密，失败时直接退出
  if decrypt_env_file "$env_name" "$encryption_key"; then
    # 显示解密结果（仅显示前几行避免泄露敏感信息）
    log_info "解密结果预览:"
    head -3 .env.local | while read line; do
      if [[ "$line" == *"="* ]]; then
        key="${line%%=*}"
        echo "  $key=***"
      else
        echo "  $line"
      fi
    done
    
    # 验证关键配置项
    local config_count=$(grep -c "^[^#].*=" .env.local || echo 0)
    if [ "$config_count" -eq 0 ]; then
      log_error "解密后的配置文件无有效配置项"
      exit 1
    fi
    
    log_info "✅ $env_name 环境配置文件准备完成 (配置项: $config_count)"
  else
    log_error "❌ $env_name 环境配置文件解密失败"
    log_error "部署流程终止，请检查:"
    log_error "  1. 加密密钥是否正确"
    log_error "  2. 加密文件是否存在且有效"
    log_error "  3. 加密算法是否匹配"
    exit 1
  fi
}

# 执行主函数
main "$@" 