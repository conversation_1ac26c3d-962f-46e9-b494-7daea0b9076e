#!/bin/bash

# 重新生成加密密钥和文件脚本
# 用于重置加密配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
  echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 生成随机密钥
generate_key() {
  openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# 备份现有文件
backup_files() {
  local timestamp=$(date +%Y%m%d_%H%M%S)
  local backup_dir="backup_encryption_$timestamp"
  
  log_info "创建备份目录: $backup_dir"
  mkdir -p "$backup_dir"
  
  local files=(".env.development.enc" ".env.production.enc")
  
  for file in "${files[@]}"; do
    if [ -f "$file" ]; then
      cp "$file" "$backup_dir/"
      log_info "已备份: $file"
    fi
  done
  
  echo "$backup_dir"
}

# 加密文件
encrypt_file() {
  local env_file="$1"
  local key="$2"
  local enc_file="${env_file}.enc"
  
  if [ ! -f "$env_file" ]; then
    log_error "环境文件不存在: $env_file"
    return 1
  fi
  
  log_info "加密文件: $env_file -> $enc_file"
  
  if openssl aes-256-cbc -a -pbkdf2 -iter 100000 -in "$env_file" -out "$enc_file" -k "$key"; then
    log_info "✅ 加密成功: $enc_file"
    log_debug "文件大小: $(wc -c < "$enc_file") 字节"
    return 0
  else
    log_error "❌ 加密失败: $env_file"
    return 1
  fi
}

# 验证解密
verify_decryption() {
  local enc_file="$1"
  local key="$2"
  local temp_file="/tmp/verify_decrypt_$$"
  
  log_info "验证解密: $enc_file"
  
  if openssl aes-256-cbc -d -a -pbkdf2 -iter 100000 -in "$enc_file" -out "$temp_file" -k "$key" 2>/dev/null; then
    if [ -s "$temp_file" ] && grep -q "=" "$temp_file" 2>/dev/null; then
      log_info "✅ 解密验证成功"
      rm -f "$temp_file"
      return 0
    fi
  fi
  
  log_error "❌ 解密验证失败"
  rm -f "$temp_file"
  return 1
}

# 主函数
main() {
  local action="${1:-interactive}"
  
  log_info "🔐 加密密钥和文件重新生成工具"
  
  # 检查必要文件
  if [ ! -f ".env.development" ] || [ ! -f ".env.production" ]; then
    log_error "缺少必要的环境文件 (.env.development 或 .env.production)"
    exit 1
  fi
  
  case "$action" in
    "interactive"|"")
      # 交互模式
      echo
      log_warn "⚠️  此操作将重新生成加密密钥并重新加密环境文件"
      log_warn "⚠️  请确保你已经备份了现有的密钥信息"
      echo
      read -p "是否继续？(y/N): " confirm
      
      if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
      fi
      
      # 备份现有文件
      backup_dir=$(backup_files)
      
      # 生成新密钥
      log_info "生成新的加密密钥..."
      dev_key=$(generate_key)
      prod_key=$(generate_key)
      
      log_info "开发环境密钥: $dev_key"
      log_info "生产环境密钥: $prod_key"
      
      # 加密文件
      if encrypt_file ".env.development" "$dev_key" && \
         encrypt_file ".env.production" "$prod_key"; then
        
        # 验证加密结果
        if verify_decryption ".env.development.enc" "$dev_key" && \
           verify_decryption ".env.production.enc" "$prod_key"; then
          
          log_info "🎉 加密完成！"
          echo
          log_info "📋 请将以下密钥设置到GitHub Secrets:"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo "DEV_ENCRYPTION_KEY=$dev_key"
          echo "PROD_ENCRYPTION_KEY=$prod_key"
          echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
          echo
          log_info "备份文件保存在: $backup_dir"
          
        else
          log_error "加密验证失败，恢复备份文件"
          cp "$backup_dir"/*.enc . 2>/dev/null || true
          exit 1
        fi
      else
        log_error "加密失败，恢复备份文件"
        cp "$backup_dir"/*.enc . 2>/dev/null || true
        exit 1
      fi
      ;;
      
    "auto")
      # 自动模式（用于脚本调用）
      backup_dir=$(backup_files)
      dev_key=$(generate_key)
      prod_key=$(generate_key)
      
      if encrypt_file ".env.development" "$dev_key" && \
         encrypt_file ".env.production" "$prod_key" && \
         verify_decryption ".env.development.enc" "$dev_key" && \
         verify_decryption ".env.production.enc" "$prod_key"; then
        
        # 输出密钥到环境变量文件
        cat > .env.encryption << EOF
DEV_ENCRYPTION_KEY=$dev_key
PROD_ENCRYPTION_KEY=$prod_key
EOF
        
        log_info "✅ 自动生成完成，密钥保存在 .env.encryption"
      else
        log_error "自动生成失败"
        exit 1
      fi
      ;;
      
    "help"|"-h"|"--help")
      echo "用法: $0 [interactive|auto|help]"
      echo
      echo "模式说明:"
      echo "  interactive  - 交互模式（默认）"
      echo "  auto         - 自动模式，密钥保存到 .env.encryption"
      echo "  help         - 显示此帮助信息"
      echo
      echo "示例:"
      echo "  $0                    # 交互模式"
      echo "  $0 interactive        # 交互模式"
      echo "  $0 auto              # 自动模式"
      ;;
      
    *)
      log_error "未知模式: $action"
      echo "使用 '$0 help' 查看帮助信息"
      exit 1
      ;;
  esac
}

# 检查依赖
if ! command -v openssl &> /dev/null; then
  log_error "需要安装 openssl"
  exit 1
fi

# 执行主函数
main "$@" 