#!/bin/bash

# 服务器部署环境设置脚本
# Server Deployment Environment Setup Script

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以root用户身份运行此脚本"
        exit 1
    fi
}

# 更新系统包
update_system() {
    log_info "更新系统包..."
    sudo apt-get update -y
    sudo apt-get upgrade -y
    log_success "系统包更新完成"
}

# 安装必需软件
install_requirements() {
    log_info "安装必需软件..."
    
    # 安装基础软件
    sudo apt-get install -y \
        curl \
        wget \
        git \
        unzip \
        nginx \
        certbot \
        python3-certbot-nginx \
        ufw \
        fail2ban
    
    log_success "必需软件安装完成"
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    # 重置防火墙规则
    sudo ufw --force reset
    
    # 设置默认策略
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # 允许SSH
    sudo ufw allow ssh
    
    # 允许HTTP和HTTPS
    sudo ufw allow 'Nginx Full'
    
    # 启用防火墙
    sudo ufw --force enable
    
    log_success "防火墙配置完成"
}

# 创建部署用户
create_deploy_user() {
    local username=${1:-deploy}
    
    log_info "创建部署用户: $username"
    
    # 检查用户是否存在
    if id "$username" &>/dev/null; then
        log_warning "用户 $username 已存在"
        return 0
    fi
    
    # 创建用户
    sudo adduser --disabled-password --gecos "" $username
    
    # 添加到sudo组
    sudo usermod -aG sudo $username
    
    # 创建SSH目录
    sudo mkdir -p /home/<USER>/.ssh
    sudo chown $username:$username /home/<USER>/.ssh
    sudo chmod 700 /home/<USER>/.ssh
    
    log_success "部署用户 $username 创建完成"
    log_warning "请手动添加SSH公钥到 /home/<USER>/.ssh/authorized_keys"
}

# 配置Nginx
setup_nginx() {
    local domain=${1:-example.com}
    local env=${2:-production}
    
    log_info "配置Nginx for $domain ($env)"
    
    # 创建部署目录
    local deploy_path="/var/www/finsight-$env"
    sudo mkdir -p $deploy_path
    sudo chown -R www-data:www-data $deploy_path
    sudo chmod -R 755 $deploy_path
    
    # 创建Nginx配置
    local config_file="/etc/nginx/sites-available/finsight-$env"
    
    if [ "$env" = "production" ]; then
        # 生产环境配置
        sudo tee $config_file > /dev/null <<EOF
server {
    listen 80;
    listen [::]:80;
    server_name $domain;
    
    # 重定向到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $domain;
    
    root $deploy_path/live;
    index index.html;
    
    # SSL配置
    ssl_certificate /etc/letsencrypt/live/$domain/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$domain/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 缓存配置
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)\$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # React路由支持
    location / {
        try_files \$uri \$uri/ /index.html;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}
EOF
    else
        # 开发环境配置
        sudo tee $config_file > /dev/null <<EOF
server {
    listen 80;
    listen [::]:80;
    server_name $domain;
    
    root $deploy_path;
    index index.html;
    
    # React路由支持
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
    fi
    
    # 启用站点
    sudo ln -sf $config_file /etc/nginx/sites-enabled/
    
    # 测试配置
    sudo nginx -t
    
    # 重启Nginx
    sudo systemctl restart nginx
    sudo systemctl enable nginx
    
    log_success "Nginx配置完成"
}

# 配置SSL证书（仅生产环境）
setup_ssl() {
    local domain=$1
    
    if [ -z "$domain" ]; then
        log_error "域名不能为空"
        return 1
    fi
    
    log_info "为 $domain 配置SSL证书..."
    
    # 获取SSL证书
    sudo certbot --nginx -d $domain --non-interactive --agree-tos --email admin@$domain
    
    # 设置自动续期
    sudo crontab -l | { cat; echo "0 12 * * * /usr/bin/certbot renew --quiet"; } | sudo crontab -
    
    log_success "SSL证书配置完成"
}

# 配置fail2ban
setup_fail2ban() {
    log_info "配置fail2ban..."
    
    # 创建jail.local配置
    sudo tee /etc/fail2ban/jail.local > /dev/null <<EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = ssh
logpath = %(sshd_log)s
backend = %(sshd_backend)s

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-noscript]
enabled = true
port = http,https
filter = nginx-noscript
logpath = /var/log/nginx/access.log
maxretry = 6

[nginx-badbots]
enabled = true
port = http,https
filter = nginx-badbots
logpath = /var/log/nginx/access.log
maxretry = 2

[nginx-noproxy]
enabled = true
port = http,https
filter = nginx-noproxy
logpath = /var/log/nginx/access.log
maxretry = 2
EOF

    # 启动fail2ban
    sudo systemctl restart fail2ban
    sudo systemctl enable fail2ban
    
    log_success "fail2ban配置完成"
}

# 优化系统性能
optimize_system() {
    log_info "优化系统性能..."
    
    # 优化文件描述符限制
    sudo tee -a /etc/security/limits.conf > /dev/null <<EOF
* soft nofile 65535
* hard nofile 65535
EOF

    # 优化网络参数
    sudo tee -a /etc/sysctl.conf > /dev/null <<EOF
# 网络优化
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 20480
net.ipv4.tcp_max_tw_buckets = 400000
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_rmem = 10240 87380 12582912
net.ipv4.tcp_wmem = 10240 87380 12582912
net.core.rmem_default = 8388608
net.core.rmem_max = 16777216
net.core.wmem_default = 8388608
net.core.wmem_max = 16777216
EOF

    # 应用内核参数
    sudo sysctl -p
    
    log_success "系统性能优化完成"
}

# 显示使用帮助
show_help() {
    echo "服务器部署环境设置脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV          环境类型 (development|production)"
    echo "  -d, --domain DOMAIN    域名"
    echo "  -u, --user USERNAME    部署用户名 (默认: deploy)"
    echo "  --ssl                  配置SSL证书 (仅生产环境)"
    echo "  --skip-update          跳过系统更新"
    echo "  -h, --help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --env production --domain finsight.com --ssl"
    echo "  $0 --env development --domain dev.finsight.com"
}

# 主函数
main() {
    local env="production"
    local domain=""
    local username="deploy"
    local setup_ssl_flag=false
    local skip_update=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                env="$2"
                shift 2
                ;;
            -d|--domain)
                domain="$2"
                shift 2
                ;;
            -u|--user)
                username="$2"
                shift 2
                ;;
            --ssl)
                setup_ssl_flag=true
                shift
                ;;
            --skip-update)
                skip_update=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证参数
    if [ "$env" != "development" ] && [ "$env" != "production" ]; then
        log_error "环境类型必须是 development 或 production"
        exit 1
    fi
    
    if [ -z "$domain" ]; then
        log_error "请指定域名"
        show_help
        exit 1
    fi
    
    log_info "开始设置 $env 环境部署服务器..."
    log_info "域名: $domain"
    log_info "用户: $username"
    
    # 执行设置步骤
    check_root
    
    if [ "$skip_update" = false ]; then
        update_system
    fi
    
    install_requirements
    setup_firewall
    create_deploy_user "$username"
    setup_nginx "$domain" "$env"
    setup_fail2ban
    optimize_system
    
    if [ "$env" = "production" ] && [ "$setup_ssl_flag" = true ]; then
        setup_ssl "$domain"
    fi
    
    log_success "服务器部署环境设置完成!"
    
    echo ""
    echo "🔑 下一步操作:"
    echo "1. 添加SSH公钥到 /home/<USER>/.ssh/authorized_keys"
    echo "2. 在GitHub仓库中配置以下Secrets:"
    if [ "$env" = "development" ]; then
        echo "   - DEV_HOST=$domain"
        echo "   - DEV_USERNAME=$username"
        echo "   - DEV_SSH_KEY=<私钥内容>"
        echo "   - DEV_DEPLOY_PATH=/var/www/finsight-$env"
        echo "   - DEV_API_BASE_URL=<API地址>"
    else
        echo "   - PROD_HOST=$domain"
        echo "   - PROD_USERNAME=$username"
        echo "   - PROD_SSH_KEY=<私钥内容>"
        echo "   - PROD_DEPLOY_PATH=/var/www/finsight-$env"
        echo "   - PROD_API_BASE_URL=<API地址>"
        echo "   - PROD_HEALTH_CHECK_URL=https://$domain/health"
    fi
    echo "3. 推送代码到对应分支触发自动部署"
}

# 运行主函数
main "$@" 