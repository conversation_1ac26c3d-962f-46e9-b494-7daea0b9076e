#!/usr/bin/env node

/**
 * 环境切换脚本
 * 用于在不同环境配置之间快速切换
 */

const fs = require('fs');
const path = require('path');

// 支持的环境列表
const ENVIRONMENTS = {
  development: '.env.development',
  production: '.env.production',
  example: '.env.example',
};

// 目标环境文件
const TARGET_ENV_FILE = '.env.local';

/**
 * 显示使用帮助
 */
function showHelp() {
  console.log(`
🔧 环境切换脚本

用法:
  node scripts/switch-env.js <environment>

支持的环境:
  development  - 开发环境
  production   - 生产环境
  example      - 示例配置

示例:
  node scripts/switch-env.js development
  node scripts/switch-env.js production

当前配置文件:
${Object.entries(ENVIRONMENTS)
  .map(([env, file]) => `  ${env.padEnd(12)} - ${file}`)
  .join('\n')}
`);
}

/**
 * 检查文件是否存在
 * @param {string} filePath 文件路径
 * @returns {boolean} 文件是否存在
 */
function fileExists(filePath) {
  try {
    fs.accessSync(filePath, fs.constants.F_OK);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 读取环境文件内容
 * @param {string} filePath 文件路径
 * @returns {string} 文件内容
 */
function readEnvFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    throw new Error(`读取文件失败: ${filePath} - ${error.message}`);
  }
}

/**
 * 写入环境文件
 * @param {string} content 文件内容
 */
function writeEnvFile(content) {
  try {
    fs.writeFileSync(TARGET_ENV_FILE, content, 'utf8');
  } catch (error) {
    throw new Error(`写入文件失败: ${TARGET_ENV_FILE} - ${error.message}`);
  }
}

/**
 * 备份当前环境文件
 */
function backupCurrentEnv() {
  if (fileExists(TARGET_ENV_FILE)) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = `.env.backup.${timestamp}`;
    
    try {
      const content = readEnvFile(TARGET_ENV_FILE);
      fs.writeFileSync(backupFile, content, 'utf8');
      console.log(`📦 已备份当前配置到: ${backupFile}`);
    } catch (error) {
      console.warn(`⚠️  备份失败: ${error.message}`);
    }
  }
}

/**
 * 切换到指定环境
 * @param {string} environment 环境名称
 */
function switchEnvironment(environment) {
  // 检查环境是否支持
  if (!ENVIRONMENTS[environment]) {
    console.error(`❌ 不支持的环境: ${environment}`);
    console.log(`\n支持的环境: ${Object.keys(ENVIRONMENTS).join(', ')}`);
    process.exit(1);
  }

  const sourceFile = ENVIRONMENTS[environment];

  // 检查源文件是否存在
  if (!fileExists(sourceFile)) {
    console.error(`❌ 环境配置文件不存在: ${sourceFile}`);
    process.exit(1);
  }

  try {
    // 备份当前配置
    backupCurrentEnv();

    // 读取源配置
    const content = readEnvFile(sourceFile);

    // 写入目标配置
    writeEnvFile(content);

    console.log(`✅ 已切换到 ${environment} 环境`);
    console.log(`📄 配置文件: ${sourceFile} → ${TARGET_ENV_FILE}`);
    
    // 显示关键配置信息
    showKeyConfig(content);

  } catch (error) {
    console.error(`❌ 切换失败: ${error.message}`);
    process.exit(1);
  }
}

/**
 * 显示关键配置信息
 * @param {string} content 配置文件内容
 */
function showKeyConfig(content) {
  const lines = content.split('\n');
  const keyConfigs = [
    'REACT_APP_ENV',
    'REACT_APP_API_BASE_URL',
    'REACT_APP_ENABLE_DEBUG_MODE',
    'REACT_APP_ENABLE_MOCK_DATA',
  ];

  console.log('\n📋 关键配置信息:');
  
  keyConfigs.forEach(key => {
    const line = lines.find(line => line.startsWith(`${key}=`));
    if (line) {
      const value = line.split('=')[1] || '';
      console.log(`  ${key}: ${value}`);
    }
  });
}

/**
 * 显示当前环境状态
 */
function showCurrentStatus() {
  console.log('📊 当前环境状态:\n');

  if (!fileExists(TARGET_ENV_FILE)) {
    console.log('❌ 未找到环境配置文件 (.env.local)');
    console.log('💡 使用以下命令设置环境:');
    console.log('   node scripts/switch-env.js development');
    return;
  }

  try {
    const content = readEnvFile(TARGET_ENV_FILE);
    const envLine = content.split('\n').find(line => line.startsWith('REACT_APP_ENV='));
    
    if (envLine) {
      const currentEnv = envLine.split('=')[1] || 'unknown';
      console.log(`🎯 当前环境: ${currentEnv}`);
    }

    showKeyConfig(content);

  } catch (error) {
    console.error(`❌ 读取配置失败: ${error.message}`);
  }
}

/**
 * 清理备份文件
 */
function cleanupBackups() {
  const files = fs.readdirSync('.').filter(file => file.startsWith('.env.backup.'));
  
  if (files.length === 0) {
    console.log('🧹 没有找到备份文件');
    return;
  }

  console.log(`🧹 发现 ${files.length} 个备份文件:`);
  files.forEach(file => {
    try {
      fs.unlinkSync(file);
      console.log(`  ✅ 已删除: ${file}`);
    } catch (error) {
      console.log(`  ❌ 删除失败: ${file} - ${error.message}`);
    }
  });
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'status':
      showCurrentStatus();
      break;
    
    case 'cleanup':
      cleanupBackups();
      break;
    
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    
    default:
      if (!command) {
        showHelp();
        process.exit(1);
      }
      
      if (Object.keys(ENVIRONMENTS).includes(command)) {
        switchEnvironment(command);
      } else {
        console.error(`❌ 未知命令: ${command}`);
        showHelp();
        process.exit(1);
      }
  }
}

// 执行主函数
main(); 