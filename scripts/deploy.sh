#!/bin/bash

# 部署脚本 - 用于安全地部署前端应用
# 作者: 前端工程师
# 用途: 处理构建产物的部署流程，包括权限设置、文件传输和解压

set -e  # 遇到错误立即退出

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
TEMP_DIR="/tmp"
ARCHIVE_NAME="app_finsight_frontend_$(date +%Y%m%d_%H%M%S).tar.gz"

# 颜色输出函数
print_info() {
    echo "ℹ️  $1"
}

print_success() {
    echo "✅ $1"
}

print_error() {
    echo "❌ $1" >&2
}

print_warning() {
    echo "⚠️  $1"
}

# 检查构建目录是否存在
check_build_directory() {
    print_info "检查构建目录..."
    
    if [ ! -d "$BUILD_DIR" ]; then
        print_error "构建目录不存在: $BUILD_DIR"
        print_info "请先运行 'npm run build' 构建项目"
        exit 1
    fi
    
    if [ ! "$(ls -A $BUILD_DIR)" ]; then
        print_error "构建目录为空: $BUILD_DIR"
        exit 1
    fi
    
    print_success "构建目录检查通过"
}

# 创建压缩包
create_archive() {
    print_info "创建部署压缩包..."
    
    cd "$BUILD_DIR"
    
    # 创建压缩包到临时目录
    tar -czf "$TEMP_DIR/$ARCHIVE_NAME" . 2>/dev/null || {
        print_error "创建压缩包失败"
        exit 1
    }
    
    # 验证压缩包
    if [ ! -f "$TEMP_DIR/$ARCHIVE_NAME" ]; then
        print_error "压缩包创建失败"
        exit 1
    fi
    
    # 显示压缩包信息
    local size=$(du -h "$TEMP_DIR/$ARCHIVE_NAME" | cut -f1)
    print_success "压缩包创建成功: $ARCHIVE_NAME (大小: $size)"
    
    cd - > /dev/null
}

# 上传文件到服务器
upload_to_server() {
    local server_host="$1"
    local server_user="$2"
    local ssh_key="$3"
    local deploy_path="$4"
    
    print_info "上传文件到服务器: $server_host"
    
    # 验证参数
    if [ -z "$server_host" ] || [ -z "$server_user" ] || [ -z "$deploy_path" ]; then
        print_error "服务器参数不完整"
        exit 1
    fi
    
    # 上传压缩包
    scp -i "$ssh_key" -o StrictHostKeyChecking=no \
        "$TEMP_DIR/$ARCHIVE_NAME" \
        "$server_user@$server_host:/tmp/" || {
        print_error "文件上传失败"
        exit 1
    }
    
    print_success "文件上传完成"
}

# 在服务器上部署
deploy_on_server() {
    local server_host="$1"
    local server_user="$2"
    local ssh_key="$3"
    local deploy_path="$4"
    local environment="$5"
    
    print_info "在服务器上执行部署..."
    
    # 创建部署脚本
    local deploy_script=$(cat << 'EOF'
#!/bin/bash
set -e

DEPLOY_PATH="$1"
ARCHIVE_NAME="$2"
ENVIRONMENT="$3"

echo "🚀 开始部署到: $DEPLOY_PATH"

# 创建必要的目录
sudo mkdir -p "$DEPLOY_PATH"
sudo mkdir -p "$DEPLOY_PATH/releases"
sudo mkdir -p "$DEPLOY_PATH/shared"

# 创建当前时间戳目录
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RELEASE_DIR="$DEPLOY_PATH/releases/$TIMESTAMP"
sudo mkdir -p "$RELEASE_DIR"

# 设置目录权限
sudo chown -R $USER:$USER "$DEPLOY_PATH"
sudo chmod -R 755 "$DEPLOY_PATH"

# 解压文件到发布目录
echo "📦 解压部署文件..."
cd "$RELEASE_DIR"
tar -xzf "/tmp/$ARCHIVE_NAME" || {
    echo "❌ 解压失败"
    exit 1
}

# 设置文件权限
sudo chown -R www-data:www-data "$RELEASE_DIR"
sudo chmod -R 755 "$RELEASE_DIR"

# 创建或更新符号链接
echo "🔗 更新应用链接..."
sudo rm -f "$DEPLOY_PATH/current"
sudo ln -sf "$RELEASE_DIR" "$DEPLOY_PATH/current"

# 清理旧版本（保留最近5个版本）
echo "🧹 清理旧版本..."
cd "$DEPLOY_PATH/releases"
ls -1t | tail -n +6 | xargs -r sudo rm -rf

# 清理临时文件
rm -f "/tmp/$ARCHIVE_NAME"

echo "✅ 部署完成"
echo "📍 当前版本: $TIMESTAMP"
echo "📁 部署路径: $DEPLOY_PATH/current"

# 如果是生产环境，重新加载nginx
if [ "$ENVIRONMENT" = "production" ]; then
    echo "🔄 重新加载Nginx配置..."
    sudo nginx -t && sudo systemctl reload nginx || {
        echo "⚠️  Nginx重载失败，请手动检查"
    }
fi
EOF
)

    # 执行远程部署脚本
    ssh -i "$ssh_key" -o StrictHostKeyChecking=no \
        "$server_user@$server_host" \
        "bash -s" -- "$deploy_path" "$ARCHIVE_NAME" "$environment" <<< "$deploy_script" || {
        print_error "远程部署失败"
        exit 1
    }
    
    print_success "服务器部署完成"
}

# 清理本地临时文件
cleanup() {
    print_info "清理临时文件..."
    
    if [ -f "$TEMP_DIR/$ARCHIVE_NAME" ]; then
        rm -f "$TEMP_DIR/$ARCHIVE_NAME"
        print_success "临时文件清理完成"
    fi
}

# 回滚函数
rollback() {
    local server_host="$1"
    local server_user="$2"
    local ssh_key="$3"
    local deploy_path="$4"
    
    print_info "执行回滚操作..."
    
    local rollback_script=$(cat << 'EOF'
#!/bin/bash
set -e

DEPLOY_PATH="$1"

echo "🔄 开始回滚..."

# 检查是否有可回滚的版本
if [ ! -d "$DEPLOY_PATH/releases" ]; then
    echo "❌ 没有可回滚的版本"
    exit 1
fi

# 获取当前版本和前一个版本
CURRENT=$(readlink "$DEPLOY_PATH/current" 2>/dev/null | xargs basename || echo "")
PREVIOUS=$(ls -1t "$DEPLOY_PATH/releases" | grep -v "$CURRENT" | head -n 1)

if [ -z "$PREVIOUS" ]; then
    echo "❌ 没有可回滚的版本"
    exit 1
fi

echo "📍 当前版本: $CURRENT"
echo "📍 回滚到版本: $PREVIOUS"

# 更新符号链接
sudo rm -f "$DEPLOY_PATH/current"
sudo ln -sf "$DEPLOY_PATH/releases/$PREVIOUS" "$DEPLOY_PATH/current"

echo "✅ 回滚完成"
echo "📁 当前版本: $PREVIOUS"
EOF
)

    ssh -i "$ssh_key" -o StrictHostKeyChecking=no \
        "$server_user@$server_host" \
        "bash -s" -- "$deploy_path" <<< "$rollback_script" || {
        print_error "回滚失败"
        exit 1
    }
    
    print_success "回滚完成"
}

# 显示使用帮助
show_help() {
    cat << EOF
部署脚本使用方法:

基本部署:
  $0 deploy <server_host> <server_user> <ssh_key_path> <deploy_path> <environment>

回滚:
  $0 rollback <server_host> <server_user> <ssh_key_path> <deploy_path>

参数说明:
  server_host    - 服务器地址
  server_user    - SSH用户名
  ssh_key_path   - SSH私钥路径
  deploy_path    - 部署目录路径
  environment    - 环境名称 (development/production)

示例:
  $0 deploy example.com ubuntu ~/.ssh/id_rsa /var/www/app production
  $0 rollback example.com ubuntu ~/.ssh/id_rsa /var/www/app

注意:
  - 确保构建目录存在且不为空
  - 确保有服务器的SSH访问权限
  - 确保部署目录有写入权限
EOF
}

# 主函数
main() {
    local command="$1"
    
    case "$command" in
        "deploy")
            if [ $# -ne 6 ]; then
                print_error "参数数量不正确"
                show_help
                exit 1
            fi
            
            local server_host="$2"
            local server_user="$3"
            local ssh_key="$4"
            local deploy_path="$5"
            local environment="$6"
            
            print_info "开始部署到 $environment 环境..."
            
            # 设置清理陷阱
            trap cleanup EXIT
            
            check_build_directory
            create_archive
            upload_to_server "$server_host" "$server_user" "$ssh_key" "$deploy_path"
            deploy_on_server "$server_host" "$server_user" "$ssh_key" "$deploy_path" "$environment"
            
            print_success "部署完成！"
            ;;
            
        "rollback")
            if [ $# -ne 5 ]; then
                print_error "参数数量不正确"
                show_help
                exit 1
            fi
            
            local server_host="$2"
            local server_user="$3"
            local ssh_key="$4"
            local deploy_path="$5"
            
            rollback "$server_host" "$server_user" "$ssh_key" "$deploy_path"
            ;;
            
        "help"|"-h"|"--help"|"")
            show_help
            ;;
            
        *)
            print_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 