{"name": "app-finsight-frontend", "version": "1.0.0", "description": "Financial Insight Frontend Application", "private": true, "author": "Development Team", "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx --fix", "lint:check": "eslint src --ext .ts,.tsx", "prettier": "prettier --write src/**/*.{ts,tsx,css,md}", "prettier:check": "prettier --check src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "env:dev": "node scripts/switch-env.js development", "env:prod": "node scripts/switch-env.js production", "env:status": "node scripts/switch-env.js status", "env:cleanup": "node scripts/switch-env.js cleanup", "start:dev": "npm run env:dev && npm start", "build:prod": "npm run env:prod && npm run build", "validate:cicd": "node scripts/validate-cicd.js", "deploy:check": "npm run validate:cicd && npm run type-check && npm run lint:check && npm run test -- --watchAll=false", "security:check": "npm audit", "security:fix": "node scripts/security-fix.js fix", "security:report": "node scripts/security-fix.js report"}, "dependencies": {"@reduxjs/toolkit": "^1.9.0", "@types/lodash": "^4.14.0", "@types/styled-components": "^5.1.0", "ahooks": "^3.7.0", "antd": "^5.0.0", "axios": "^1.3.0", "classnames": "^2.3.0", "dayjs": "^1.11.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.0", "react-redux": "^8.0.0", "react-router-dom": "^6.8.0", "styled-components": "^5.3.0", "typescript": "^4.9.5"}, "devDependencies": {"@craco/craco": "^7.1.0", "@testing-library/jest-dom": "^5.16.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/node": "^16.18.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "craco-alias": "^3.0.1", "eslint": "^8.0.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-import": "^2.27.0", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^4.6.0", "js-yaml": "^4.1.0", "prettier": "^2.8.0", "react-scripts": "5.0.1", "webpack-bundle-analyzer": "^4.7.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.31", "webpack-dev-server": "^4.15.1"}}