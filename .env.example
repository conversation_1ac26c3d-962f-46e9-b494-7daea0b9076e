# 应用配置
REACT_APP_NAME=FinSight Frontend
REACT_APP_VERSION=1.0.0
REACT_APP_DESCRIPTION=Financial Insight Frontend Application

# 环境配置
NODE_ENV=development
REACT_APP_ENV=development

# API 配置
REACT_APP_API_BASE_URL=http://localhost:3001/api
REACT_APP_API_VERSION=v1
REACT_APP_API_TIMEOUT=30000

# 认证配置
REACT_APP_JWT_SECRET_KEY=your-jwt-secret-key
REACT_APP_JWT_EXPIRES_IN=7d
REACT_APP_REFRESH_TOKEN_EXPIRES_IN=30d

# 第三方服务配置
REACT_APP_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
REACT_APP_SENTRY_DSN=https://your-sentry-dsn.ingest.sentry.io

# 功能开关
REACT_APP_ENABLE_MOCK_DATA=false
REACT_APP_ENABLE_DEBUG_MODE=false
REACT_APP_ENABLE_HOT_RELOAD=true

# 上传配置
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# 缓存配置
REACT_APP_CACHE_TTL=300000
REACT_APP_LOCAL_STORAGE_PREFIX=finsight_

# WebSocket 配置
REACT_APP_WS_URL=ws://localhost:3001
REACT_APP_WS_RECONNECT_INTERVAL=5000

# 主题配置
REACT_APP_DEFAULT_THEME=light
REACT_APP_PRIMARY_COLOR=#1890ff
REACT_APP_SUCCESS_COLOR=#52c41a
REACT_APP_WARNING_COLOR=#faad14
REACT_APP_ERROR_COLOR=#f5222d

# 分页配置
REACT_APP_DEFAULT_PAGE_SIZE=20
REACT_APP_MAX_PAGE_SIZE=100

# CDN 配置
REACT_APP_CDN_BASE_URL=https://cdn.example.com
REACT_APP_STATIC_ASSETS_URL=https://static.example.com
