name: 生产环境 CI/CD Pipeline

on:
  push:
    branches:
      - main         # 仅生产分支触发
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: '18.x'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

permissions:
  contents: write  # 允许创建 release 和 tag

jobs:
  # 代码质量检查和测试
  test-and-lint:
    name: 代码质量检查和测试
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 安装依赖
        run: npm ci
        
      - name: TypeScript 类型检查
        run: npm run type-check
        
      - name: ESLint 代码检查
        run: npm run lint:check
        
      - name: Prettier 格式检查
        run: npm run prettier:check
        
      - name: 运行单元测试
        run: npm test -- --coverage --watchAll=false

      - name: 生产环境安全扫描
        run: |
          echo "🔍 执行生产环境安全扫描..."
          
          # 首先尝试自动修复安全漏洞
          echo "🔧 尝试自动修复安全漏洞..."
          npm run security:fix || {
            echo "⚠️ 自动修复失败，生成详细报告..."
            npm run security:report
            
            # 检查是否存在高危漏洞
            HIGH_VULN=$(npm audit --json 2>/dev/null | jq '.metadata.vulnerabilities.high // 0' 2>/dev/null || echo "0")
            CRITICAL_VULN=$(npm audit --json 2>/dev/null | jq '.metadata.vulnerabilities.critical // 0' 2>/dev/null || echo "0")
            
            echo "高危漏洞数量: $HIGH_VULN"
            echo "严重漏洞数量: $CRITICAL_VULN"
            
            # 如果存在高危或严重漏洞，阻止部署
            if [ "$HIGH_VULN" -gt "0" ] || [ "$CRITICAL_VULN" -gt "0" ]; then
              echo "❌ 发现高危或严重安全漏洞，阻止生产部署"
              echo "请运行 'npm run security:fix' 修复漏洞后重新部署"
              exit 1
            else
              echo "⚠️ 仅存在中低危漏洞，允许继续部署但建议修复"
            fi
          }
          
          # 检查源码中的敏感信息
          echo "🔍 检查源码中的敏感信息..."
          SENSITIVE_FOUND=false
          
          # 检查明文密码、密钥等
          if grep -r -i "password.*=.*['\"][^'\"]*['\"]" src/ --exclude-dir=node_modules --exclude="*.test.*" --exclude="*.spec.*" 2>/dev/null; then
            echo "⚠️ 发现可能的明文密码"
            SENSITIVE_FOUND=true
          fi
          
          if grep -r -i "secret.*=.*['\"][^'\"]*['\"]" src/ --exclude-dir=node_modules --exclude="*.test.*" --exclude="*.spec.*" 2>/dev/null; then
            echo "⚠️ 发现可能的密钥信息"
            SENSITIVE_FOUND=true
          fi
          
          if grep -r -i "token.*=.*['\"][^'\"]*['\"]" src/ --exclude-dir=node_modules --exclude="*.test.*" --exclude="*.spec.*" 2>/dev/null; then
            echo "⚠️ 发现可能的令牌信息"
            SENSITIVE_FOUND=true
          fi
          
          # 检查API密钥模式
          if grep -r -E "(api[_-]?key|access[_-]?key|private[_-]?key).*=.*['\"][a-zA-Z0-9]{20,}['\"]" src/ --exclude-dir=node_modules --exclude="*.test.*" --exclude="*.spec.*" 2>/dev/null; then
            echo "⚠️ 发现可能的API密钥"
            SENSITIVE_FOUND=true
          fi
          
          if [ "$SENSITIVE_FOUND" = true ]; then
            echo "❌ 发现敏感信息，请检查并移除后重新部署"
            exit 1
          fi
          
          echo "✅ 生产环境安全扫描完成"
        
  # 构建生产环境应用
  build-production:
    name: 构建生产环境应用
    runs-on: ubuntu-latest
    needs: test-and-lint
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 安装依赖
        run: npm ci
        
      - name: 解密生产环境配置文件
        env:
          PROD_ENCRYPTION_KEY: ${{ secrets.PROD_ENCRYPTION_KEY }}
          ENVIRONMENT: production
        run: |
          # 确保解密脚本有执行权限
          chmod +x ./scripts/decrypt-env.sh
          
          echo "🔍 开始解密生产环境配置文件..."
          echo "目标环境: $ENVIRONMENT"
          
          # 执行解密脚本（脚本内部会验证密钥和进行错误处理）
          ./scripts/decrypt-env.sh
          
          echo "✅ 生产环境配置文件解密完成"
          
      - name: 构建生产环境应用
        run: npm run build
        
      - name: 生产构建质量检查
        run: |
          echo "🔍 执行生产构建质量检查..."
          
          # 检查构建产物大小
          BUILD_SIZE=$(du -sm build | cut -f1)
          echo "📊 构建产物大小: ${BUILD_SIZE}MB"
          
          if [ $BUILD_SIZE -gt 100 ]; then
            echo "⚠️ 构建产物较大 (${BUILD_SIZE}MB)，请检查是否有优化空间"
          fi
          
          # 检查是否包含调试信息
          if grep -r "console.log\|debugger\|TODO\|FIXME" build/ --exclude="*.map" --exclude="*.txt"; then
            echo "❌ 发现调试信息或待修复项目，请清理后重新部署"
            exit 1
          fi
          
          # 检查关键文件是否存在
          if [ ! -f "build/index.html" ]; then
            echo "❌ 构建产物不完整，缺少 index.html"
            exit 1
          fi
          
          if [ ! -d "build/static" ]; then
            echo "❌ 构建产物不完整，缺少 static 目录"
            exit 1
          fi
          
          echo "✅ 生产构建质量检查通过"
        
      - name: 压缩构建产物
        run: |
          cd build
          tar -czf ../build-production.tar.gz .
          
      - name: 上传构建产物
        uses: actions/upload-artifact@v4
        with:
          name: build-production
          path: build-production.tar.gz
          retention-days: 30

  # 生产环境部署
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: build-production
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 下载构建产物
        uses: actions/download-artifact@v4
        with:
          name: build-production
          
      - name: 解压构建产物
        run: |
          mkdir -p build
          tar -xzf build-production.tar.gz -C build
          
      - name: 生产环境最终安全检查
        run: |
          echo "🔍 执行生产环境最终安全检查..."
          
          # 最后一次检查敏感信息泄露
          if grep -r "console.log\|debugger\|TODO\|FIXME" build/ --exclude="*.map"; then
            echo "❌ 发现调试信息或待修复项目，请清理后重新部署"
            exit 1
          fi
          
          # 检查构建产物完整性
          if [ ! -f "build/index.html" ] || [ ! -d "build/static" ]; then
            echo "❌ 构建产物不完整"
            exit 1
          fi
          
          # 检查文件权限
          find build -type f -perm /o+w | head -10 | while read file; do
            echo "⚠️ 文件权限过于宽松: $file"
          done
          
          echo "✅ 生产环境最终安全检查通过"
          
      - name: 创建部署备份
        run: |
          echo "📦 创建部署备份..."
          
          # 创建带时间戳的备份文件
          TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
          BACKUP_NAME="production_backup_${TIMESTAMP}.tar.gz"
          
          tar -czf $BACKUP_NAME build/
          
          echo "✅ 部署备份已创建: $BACKUP_NAME"
          
      - name: 部署到生产服务器
        run: |
          # 检查脚本文件是否存在
          if [ ! -f "./scripts/deploy.sh" ]; then
            echo "❌ 部署脚本不存在: ./scripts/deploy.sh"
            exit 1
          fi
          
          # 确保脚本有执行权限
          chmod +x ./scripts/deploy.sh
          
          # 准备SSH密钥
          echo "${{ secrets.PROD_SSH_KEY }}" > /tmp/ssh_key
          chmod 600 /tmp/ssh_key
          
          # 使用部署脚本
          ./scripts/deploy.sh deploy \
            "${{ secrets.PROD_HOST }}" \
            "${{ secrets.PROD_USERNAME }}" \
            "/tmp/ssh_key" \
            "${{ secrets.PROD_DEPLOY_PATH }}" \
            "production"
          
          # 清理SSH密钥
          rm -f /tmp/ssh_key
          
      - name: 生产环境健康检查
        run: |
          echo "🏥 执行生产环境健康检查..."
          
          # 等待服务启动
          sleep 30
          
          # 检查服务是否正常运行
          if command -v curl >/dev/null 2>&1; then
            HEALTH_URL="${{ secrets.PROD_HEALTH_CHECK_URL }}"
            if [ -n "$HEALTH_URL" ]; then
              echo "检查健康端点: $HEALTH_URL"
              if curl -f -s "$HEALTH_URL" > /dev/null; then
                echo "✅ 生产环境服务健康检查通过"
              else
                echo "❌ 生产环境服务健康检查失败"
                exit 1
              fi
            else
              echo "⚠️ 未配置健康检查URL，跳过服务健康检查"
            fi
          else
            echo "⚠️ curl 命令不可用，跳过服务健康检查"
          fi
            
      - name: 创建GitHub Release
        if: success()
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ github.run_number }}-prod
          release_name: Production Release v${{ github.run_number }}
          body: |
            ## 🚀 生产环境发布
            
            **发布时间**: $(date -u +"%Y-%m-%dT%H:%M:%SZ")
            **提交**: ${{ github.sha }}
            **分支**: ${{ github.ref }}
            
            ### 📋 发布内容
            - 代码质量检查: ✅ 通过
            - 单元测试: ✅ 通过
            - 安全扫描: ✅ 通过
            - 构建质量检查: ✅ 通过
            - 最终安全检查: ✅ 通过
            - 健康检查: ✅ 通过
            
            ### 🔧 技术栈信息
            - Node.js: ${{ env.NODE_VERSION }}
            - 构建环境: Ubuntu Latest
            - 部署时间: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
            
          draft: false
          prerelease: false
          
      - name: 通知生产部署完成
        if: success()
        run: |
          echo "🎉 生产环境部署完成!"
          echo "📋 发布信息:"
          echo "  - 版本: v${{ github.run_number }}-prod"
          echo "  - 提交: ${{ github.sha }}"
          echo "  - 时间: $(date -u +"%Y-%m-%d %H:%M:%S UTC")"
          
      - name: 部署失败处理
        if: failure()
        run: |
          echo "❌ 生产环境部署失败!"
          echo "请检查以下项目:"
          echo "  1. 构建产物是否完整"
          echo "  2. 服务器连接是否正常"
          echo "  3. 部署脚本是否有权限"
          echo "  4. 健康检查端点是否可访问"
          echo ""
          echo "💡 如需回滚，请使用 rollback.yml 工作流"

    #   - name: 发送生产部署通知
    #     if: always()
    #     uses: 8398a7/action-slack@v3
    #     with:
    #       status: ${{ job.status }}
    #       channel: '#production-deployments'
    #       fields: repo,message,commit,author,action,eventName,ref,workflow,job,took
    #     env:
    #       SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # 清理生产环境构建产物
  cleanup-production:
    name: 清理生产环境构建产物
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always()
    
    steps:
      - name: 清理生产环境构建缓存
        run: |
          echo "🧹 生产环境清理完成" 