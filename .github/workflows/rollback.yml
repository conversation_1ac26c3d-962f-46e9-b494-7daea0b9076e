name: 回滚部署

on:
  workflow_dispatch:
    inputs:
      environment:
        description: '要回滚的环境'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - production
      reason:
        description: '回滚原因'
        required: true
        type: string

env:
  NODE_VERSION: '18.x'

jobs:
  rollback:
    name: 回滚部署
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 验证回滚权限
        run: |
          echo "🔒 验证回滚权限..."
          echo "环境: ${{ github.event.inputs.environment }}"
          echo "操作者: ${{ github.actor }}"
          echo "原因: ${{ github.event.inputs.reason }}"
          
      - name: 执行回滚
        run: |
          # 检查脚本文件是否存在
          if [ ! -f "./scripts/deploy.sh" ]; then
            echo "❌ 部署脚本不存在: ./scripts/deploy.sh"
            exit 1
          fi
          
          # 确保脚本有执行权限
          chmod +x ./scripts/deploy.sh
          
          # 准备SSH密钥
          if [ "${{ github.event.inputs.environment }}" = "production" ]; then
            echo "${{ secrets.PROD_SSH_KEY }}" > /tmp/ssh_key
            HOST="${{ secrets.PROD_HOST }}"
            USER="${{ secrets.PROD_USERNAME }}"
            DEPLOY_PATH="${{ secrets.PROD_DEPLOY_PATH }}"
          else
            echo "${{ secrets.DEV_SSH_KEY }}" > /tmp/ssh_key
            HOST="${{ secrets.DEV_HOST }}"
            USER="${{ secrets.DEV_USERNAME }}"
            DEPLOY_PATH="${{ secrets.DEV_DEPLOY_PATH }}"
          fi
          
          chmod 600 /tmp/ssh_key
          
          # 执行回滚
          ./scripts/deploy.sh rollback \
            "$HOST" \
            "$USER" \
            "/tmp/ssh_key" \
            "$DEPLOY_PATH"
          
          # 清理SSH密钥
          rm -f /tmp/ssh_key
          
      - name: 创建回滚记录
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: rollback-${{ github.event.inputs.environment }}-${{ github.run_number }}
          release_name: 回滚 ${{ github.event.inputs.environment }} 环境
          body: |
            ## 🔄 环境回滚
            
            **环境**: ${{ github.event.inputs.environment }}
            **操作者**: ${{ github.actor }}
            **时间**: ${{ steps.date.outputs.date }}
            **原因**: ${{ github.event.inputs.reason }}
            
            ### 📋 回滚信息
            - 回滚环境: ${{ github.event.inputs.environment }}
            - 触发分支: ${{ github.ref }}
            - 操作状态: ✅ 成功
            
          draft: false
          prerelease: true
          
      # - name: 发送回滚通知
      #   if: always()
      #   uses: 8398a7/action-slack@v3
      #   with:
      #     status: ${{ job.status }}
      #     channel: '#deployments'
      #     title: '🔄 环境回滚'
      #     text: |
      #       环境: ${{ github.event.inputs.environment }}
      #       操作者: ${{ github.actor }}
      #       原因: ${{ github.event.inputs.reason }}
      #       状态: ${{ job.status == 'success' && '成功' || '失败' }}
      #   env:
      #     SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }} 