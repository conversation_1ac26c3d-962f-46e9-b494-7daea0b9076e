name: 开发环境 CI/CD Pipeline

on:
  push:
    branches:
      - develop      # 仅开发分支触发
  pull_request:
    branches:
      - develop

env:
  NODE_VERSION: '18.x'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

permissions:
  contents: write  # 允许创建 release 和 tag

jobs:
  # 代码质量检查和测试
  test-and-lint:
    name: 代码质量检查和测试
    runs-on: ubuntu-latest
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 安装依赖
        run: npm ci
        
      - name: TypeScript 类型检查
        run: npm run type-check
        
      - name: ESLint 代码检查
        run: npm run lint:check
        
      - name: Prettier 格式检查
        run: npm run prettier:check
        
      - name: 运行单元测试
        run: npm test -- --coverage --watchAll=false
        
  # 构建开发环境应用
  build-development:
    name: 构建开发环境应用
    runs-on: ubuntu-latest
    needs: test-and-lint
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 安装依赖
        run: npm ci
        
      - name: 解密开发环境配置文件
        env:
          DEV_ENCRYPTION_KEY: ${{ secrets.DEV_ENCRYPTION_KEY }}
          ENVIRONMENT: development
        run: |
          # 确保解密脚本有执行权限
          chmod +x ./scripts/decrypt-env.sh
          
          echo "🔍 开始解密开发环境配置文件..."
          echo "目标环境: $ENVIRONMENT"
          
          # 执行解密脚本（脚本内部会验证密钥和进行错误处理）
          ./scripts/decrypt-env.sh
          
          echo "✅ 开发环境配置文件解密完成"
          
      - name: 构建开发环境应用
        run: npm run build
        
      - name: 压缩构建产物
        run: |
          cd build
          tar -czf ../build-development.tar.gz .
          
      - name: 上传构建产物
        uses: actions/upload-artifact@v4
        with:
          name: build-development
          path: build-development.tar.gz
          retention-days: 30

  # 开发环境部署
  deploy-development:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: build-development
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: development
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        
      - name: 下载构建产物
        uses: actions/download-artifact@v4
        with:
          name: build-development
          
      - name: 解压构建产物
        run: |
          mkdir -p build
          tar -xzf build-development.tar.gz -C build
          
      - name: 开发环境健康检查
        run: |
          echo "🔍 执行开发环境健康检查..."
          
          # 检查构建产物完整性
          if [ ! -f "build/index.html" ]; then
            echo "❌ 构建产物不完整，缺少 index.html"
            exit 1
          fi
          
          # 检查重要资源文件
          if [ ! -d "build/static" ]; then
            echo "❌ 构建产物不完整，缺少 static 目录"
            exit 1
          fi
          
          echo "✅ 开发环境健康检查通过"
          
      - name: 部署到开发服务器
        run: |
          # 检查脚本文件是否存在
          if [ ! -f "./scripts/deploy.sh" ]; then
            echo "❌ 部署脚本不存在: ./scripts/deploy.sh"
            exit 1
          fi
          
          # 确保脚本有执行权限
          chmod +x ./scripts/deploy.sh
          
          # 准备SSH密钥
          echo "${{ secrets.DEV_SSH_KEY }}" > /tmp/ssh_key
          chmod 600 /tmp/ssh_key
          
          # 使用部署脚本
          ./scripts/deploy.sh deploy \
            "${{ secrets.DEV_HOST }}" \
            "${{ secrets.DEV_USERNAME }}" \
            "/tmp/ssh_key" \
            "${{ secrets.DEV_DEPLOY_PATH }}" \
            "development"
          
          # 清理SSH密钥
          rm -f /tmp/ssh_key
          
      - name: 创建开发环境发布标签
        if: success()
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
          # 创建开发环境标签
          TAG_NAME="dev-v${{ github.run_number }}"
          git tag $TAG_NAME
          git push origin $TAG_NAME
          
          echo "✅ 开发环境发布标签已创建: $TAG_NAME"

  # 清理开发环境构建产物
  cleanup-development:
    name: 清理开发环境构建产物
    runs-on: ubuntu-latest
    needs: [deploy-development]
    if: always()
    
    steps:
      - name: 清理开发环境构建缓存
        run: |
          echo "🧹 开发环境清理完成" 