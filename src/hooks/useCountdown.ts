/**
 * 倒计时Hook
 */
import { useState, useEffect, useCallback } from 'react';

interface UseCountdownOptions {
  initialSeconds?: number;
  onComplete?: () => void;
}

interface UseCountdownReturn {
  seconds: number;
  isActive: boolean;
  start: (seconds?: number) => void;
  pause: () => void;
  resume: () => void;
  reset: () => void;
  isCompleted: boolean;
}

/**
 * 倒计时Hook
 * @param options 配置选项
 * @returns 倒计时状态和控制方法
 */
export const useCountdown = (
  options: UseCountdownOptions = {}
): UseCountdownReturn => {
  const { initialSeconds = 60, onComplete } = options;

  const [seconds, setSeconds] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isActive && seconds > 0) {
      interval = setInterval(() => {
        setSeconds(prevSeconds => {
          const newSeconds = prevSeconds - 1;

          if (newSeconds <= 0) {
            setIsActive(false);
            setIsCompleted(true);
            onComplete?.();
            return 0;
          }

          return newSeconds;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isActive, seconds, onComplete]);

  /**
   * 开始倒计时
   * @param seconds 倒计时秒数
   */
  const start = useCallback(
    (startSeconds?: number) => {
      const countdownSeconds = startSeconds ?? initialSeconds;
      setSeconds(countdownSeconds);
      setIsActive(true);
      setIsCompleted(false);
    },
    [initialSeconds]
  );

  /**
   * 暂停倒计时
   */
  const pause = useCallback(() => {
    setIsActive(false);
  }, []);

  /**
   * 恢复倒计时
   */
  const resume = useCallback(() => {
    if (seconds > 0) {
      setIsActive(true);
    }
  }, [seconds]);

  /**
   * 重置倒计时
   */
  const reset = useCallback(() => {
    setSeconds(0);
    setIsActive(false);
    setIsCompleted(false);
  }, []);

  return {
    seconds,
    isActive,
    start,
    pause,
    resume,
    reset,
    isCompleted,
  };
};

export default useCountdown;
