import { useCallback, useState } from 'react';
import {
  config,
  AppConfig,
  Environment,
  isDevelopment,
  isProduction,
} from '@/config/env';
import { logger } from '@/utils/logger';

/**
 * 使用应用配置的 Hook
 * @returns 配置对象和相关方法
 */
export const useConfig = () => {
  const [currentConfig, setCurrentConfig] = useState<AppConfig>(config);
  const isConfigLoaded = true;

  /**
   * 获取指定路径的配置值
   * @param path 配置路径，如 'api.baseUrl'
   * @returns 配置值
   */
  const getConfigValue = useCallback(
    (path: string): unknown => {
      const keys = path.split('.');
      let value: unknown = currentConfig;

      for (const key of keys) {
        value = (value as Record<string, unknown>)[key];
        if (value === undefined) {
          return undefined;
        }
      }

      return value;
    },
    [currentConfig]
  );

  /**
   * 检查功能是否启用
   * @param featureName 功能名称
   * @returns 是否启用
   */
  const isFeatureEnabled = useCallback(
    (featureName: keyof AppConfig['features']): boolean => {
      return currentConfig.features[featureName] || false;
    },
    [currentConfig.features]
  );

  /**
   * 获取主题配置
   * @returns 主题配置对象
   */
  const getThemeConfig = useCallback(() => {
    return currentConfig.theme;
  }, [currentConfig.theme]);

  /**
   * 获取 API 配置
   * @returns API 配置对象
   */
  const getApiConfig = useCallback(() => {
    return currentConfig.api;
  }, [currentConfig.api]);

  /**
   * 获取分页配置
   * @returns 分页配置对象
   */
  const getPaginationConfig = useCallback(() => {
    return currentConfig.pagination;
  }, [currentConfig.pagination]);

  /**
   * 动态更新配置（仅开发环境）
   * @param updates 配置更新对象
   */
  const updateConfig = useCallback((updates: Partial<AppConfig>) => {
    if (isDevelopment) {
      setCurrentConfig(prev => ({
        ...prev,
        ...updates,
      }));
      logger.log('🔄 配置已更新:', updates);
    } else {
      logger.warn('⚠️ 配置更新仅在开发环境下可用');
    }
  }, []);

  /**
   * 重置配置到初始状态
   */
  const resetConfig = useCallback(() => {
    if (isDevelopment) {
      setCurrentConfig(config);
      logger.log('🔄 配置已重置');
    }
  }, []);

  return {
    config: currentConfig,
    isConfigLoaded,
    environment: currentConfig.environment,
    isDevelopment,
    isProduction,

    // 配置获取方法
    getConfigValue,
    isFeatureEnabled,
    getThemeConfig,
    getApiConfig,
    getPaginationConfig,

    // 配置更新方法（仅开发环境）
    updateConfig,
    resetConfig,
  };
};

/**
 * 使用环境检查的 Hook
 * @returns 环境检查相关方法
 */
export const useEnvironment = () => {
  const { environment } = useConfig();

  const checkEnvironment = useCallback(
    (targetEnv: Environment): boolean => {
      return environment === targetEnv;
    },
    [environment]
  );

  const isDev = useCallback(
    () => checkEnvironment(Environment.DEVELOPMENT),
    [checkEnvironment]
  );
  const isProd = useCallback(
    () => checkEnvironment(Environment.PRODUCTION),
    [checkEnvironment]
  );
  const isTest = useCallback(
    () => checkEnvironment(Environment.TEST),
    [checkEnvironment]
  );

  return {
    environment,
    isDev,
    isProd,
    isTest,
    checkEnvironment,
  };
};

/**
 * 使用功能开关的 Hook
 * @returns 功能开关相关方法
 */
export const useFeatureFlags = () => {
  const { isFeatureEnabled, config } = useConfig();

  return {
    features: config.features,
    isFeatureEnabled,
    isMockDataEnabled: isFeatureEnabled('enableMockData'),
    isDebugModeEnabled: isFeatureEnabled('enableDebugMode'),
    isHotReloadEnabled: isFeatureEnabled('enableHotReload'),
  };
};

/**
 * 使用 API 配置的 Hook
 * @returns API 配置相关方法
 */
export const useApiConfig = () => {
  const { getApiConfig } = useConfig();
  const apiConfig = getApiConfig();

  /**
   * 构建完整的 API URL
   * @param endpoint API 端点
   * @returns 完整的 API URL
   */
  const buildApiUrl = useCallback(
    (endpoint: string): string => {
      const baseUrl = apiConfig.baseUrl.replace(/\/$/, ''); // 移除结尾的斜杠
      const cleanEndpoint = endpoint.replace(/^\//, ''); // 移除开头的斜杠
      return `${baseUrl}/${cleanEndpoint}`;
    },
    [apiConfig.baseUrl]
  );

  /**
   * 构建版本化的 API URL
   * @param endpoint API 端点
   * @param version API 版本（可选，默认使用配置中的版本）
   * @returns 版本化的 API URL
   */
  const buildVersionedApiUrl = useCallback(
    (endpoint: string, version?: string): string => {
      const apiVersion = version || apiConfig.version;
      const versionedEndpoint = `${apiVersion}/${endpoint.replace(/^\//, '')}`;
      return buildApiUrl(versionedEndpoint);
    },
    [apiConfig.version, buildApiUrl]
  );

  return {
    apiConfig,
    buildApiUrl,
    buildVersionedApiUrl,
  };
};

/**
 * 使用主题配置的 Hook
 * @returns 主题配置相关方法
 */
export const useThemeConfig = () => {
  const { getThemeConfig } = useConfig();
  const themeConfig = getThemeConfig();

  /**
   * 获取颜色值
   * @param colorType 颜色类型
   * @returns 颜色值
   */
  const getColor = useCallback(
    (colorType: keyof AppConfig['theme']): string => {
      return themeConfig[colorType] as string;
    },
    [themeConfig]
  );

  return {
    themeConfig,
    getColor,
    primaryColor: themeConfig.primaryColor,
    successColor: themeConfig.successColor,
    warningColor: themeConfig.warningColor,
    errorColor: themeConfig.errorColor,
    defaultTheme: themeConfig.defaultTheme,
  };
};
