/**
 * 主应用组件
 */
import React from 'react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider, theme as antdTheme } from 'antd';
import zhCN from 'antd/locale/zh_CN';

import { store } from '@store/index';
import AppRouter from '@routes/AppRouter';
import GlobalStyles from '@styles/GlobalStyles';
import { colors } from '@styles/theme';

/**
 * Ant Design 暗色主题配置
 */
const customTheme = {
  algorithm: antdTheme.darkAlgorithm,
  token: {
    // 基础色彩
    colorPrimary: colors.primary,
    colorSuccess: colors.success,
    colorWarning: colors.warning,
    colorError: colors.error,
    colorInfo: colors.info,

    // 背景色
    colorBgBase: colors.background.primary,
    colorBgContainer: colors.background.card,
    colorBgLayout: colors.background.secondary,

    // 边框色
    colorBorder: colors.border.default,
    colorBorderSecondary: colors.border.hover,

    // 文字色
    colorText: colors.text.primary,
    colorTextSecondary: colors.text.secondary,
    colorTextTertiary: colors.text.tertiary,
    colorTextQuaternary: colors.text.placeholder,

    // 圆角
    borderRadius: 8,
    borderRadiusLG: 16,
    borderRadiusSM: 6,

    // 字体
    fontSize: 16,
    fontSizeSM: 14,
    fontSizeLG: 18,
    fontSizeXL: 20,
    fontSizeHeading1: 28,
    fontSizeHeading2: 24,
    fontSizeHeading3: 20,

    // 间距
    padding: 16,
    paddingSM: 8,
    paddingLG: 24,
    paddingXL: 32,

    // 控件高度
    controlHeight: 40,
    controlHeightSM: 32,
    controlHeightLG: 48,

    // 阴影
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4)',
    boxShadowSecondary: '0 4px 16px rgba(0, 0, 0, 0.2)',
  },
  components: {
    Layout: {
      bodyBg: colors.background.primary,
      headerBg: colors.background.card,
      siderBg: colors.background.card,
    },
    Menu: {
      itemBg: 'transparent',
      itemSelectedBg: colors.primary,
      itemHoverBg: colors.background.secondary,
      itemColor: colors.text.secondary,
      itemSelectedColor: colors.text.primary,
    },
    Card: {
      headerBg: colors.background.card,
      actionsBg: colors.background.card,
    },
    Button: {
      primaryShadow: 'none',
    },
    Input: {
      activeBorderColor: colors.primary,
      hoverBorderColor: colors.border.hover,
    },
    Table: {
      headerBg: colors.background.secondary,
      bodySortBg: colors.background.card,
    },
  },
};

/**
 * 应用根组件
 */
const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider locale={zhCN} theme={customTheme}>
        <BrowserRouter>
          <GlobalStyles />
          <AppRouter />
        </BrowserRouter>
      </ConfigProvider>
    </Provider>
  );
};

export default App;
