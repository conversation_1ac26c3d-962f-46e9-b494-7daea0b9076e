/**
 * 应用路由配置
 */
import React, { Suspense, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Spin } from 'antd';

import { ROUTES } from '@constants/index';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { restoreAuthState } from '@store/slices/authSlice';
import MainLayout from '@layouts/MainLayout';
import ProtectedRoute from './ProtectedRoute';

// 懒加载页面组件
const LoginPage = React.lazy(() => import('@pages/Login'));
const Dashboard = React.lazy(() => import('@pages/Dashboard'));
const Reports = React.lazy(() => import('@pages/Reports'));
const Settings = React.lazy(() => import('@pages/Settings'));

/**
 * 页面加载中组件
 */
const PageLoading: React.FC = () => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
    }}
  >
    <Spin size='large' />
  </div>
);

/**
 * 应用路由组件
 */
const AppRouter: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector(state => state.auth);

  // 应用启动时恢复认证状态
  useEffect(() => {
    dispatch(restoreAuthState());
  }, [dispatch]);

  return (
    <Suspense fallback={<PageLoading />}>
      <Routes>
        {/* 登录页面 */}
        <Route
          path={ROUTES.LOGIN}
          element={
            isAuthenticated ? (
              <Navigate to={ROUTES.DASHBOARD} replace />
            ) : (
              <LoginPage />
            )
          }
        />

        {/* 需要认证的路由 */}
        <Route
          path='/'
          element={
            <ProtectedRoute>
              <MainLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to={ROUTES.DASHBOARD} replace />} />
          <Route path={ROUTES.DASHBOARD} element={<Dashboard />} />
          <Route path={ROUTES.REPORTS} element={<Reports />} />
          <Route path={ROUTES.SETTINGS} element={<Settings />} />
        </Route>

        {/* 404 页面 - 重定向到登录或仪表板 */}
        <Route
          path='*'
          element={
            <Navigate
              to={isAuthenticated ? ROUTES.DASHBOARD : ROUTES.LOGIN}
              replace
            />
          }
        />
      </Routes>
    </Suspense>
  );
};

export default AppRouter;
