/**
 * 路由保护组件
 */
import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';

import { useAppSelector, useAppDispatch } from '@/hooks/redux';
import { getCurrentUser, refreshToken } from '@store/slices/authSlice';
import { TokenManager } from '@services/api';
import { ROUTES } from '@constants/index';
import { logger } from '@/utils/logger';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * 路由保护组件
 * 检查用户认证状态，未登录用户重定向到登录页面
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const { isAuthenticated, user, loading } = useAppSelector(
    state => state.auth
  );

  useEffect(() => {
    const checkAuth = async () => {
      // 如果有token但没有用户信息，尝试获取用户信息
      if (isAuthenticated && !user) {
        try {
          await dispatch(getCurrentUser()).unwrap();
        } catch (error) {
          logger.error('获取用户信息失败:', error);
        }
      }

      // 检查token是否即将过期，如果是则刷新
      if (isAuthenticated && TokenManager.isTokenExpiringSoon()) {
        try {
          await dispatch(refreshToken()).unwrap();
        } catch (error) {
          logger.error('刷新token失败:', error);
        }
      }
    };

    if (isAuthenticated) {
      checkAuth();
    }
  }, [isAuthenticated, user, dispatch]);

  // 正在验证认证状态时显示加载
  if (loading && !isAuthenticated) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Spin size='large' />
      </div>
    );
  }

  // 未认证用户重定向到登录页面
  if (!isAuthenticated) {
    return (
      <Navigate to={ROUTES.LOGIN} state={{ from: location.pathname }} replace />
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
