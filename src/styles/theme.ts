/**
 * UI主题配置
 * 基于设计规范定义的暗色主题
 */

// 色彩系统
export const colors = {
  // 背景色
  background: {
    primary: '#0c0c0c', // 主背景
    secondary: '#1a1a1a', // 次级背景
    card: '#111111', // 卡片背景
  },

  // 边框色
  border: {
    default: '#333333', // 默认边框
    hover: '#555555', // 悬停边框
    focus: '#4f46e5', // 聚焦边框
  },

  // 主题色
  primary: '#4f46e5', // 品牌蓝
  success: '#10b981', // 成功色
  warning: '#f59e0b', // 警告色
  error: '#ef4444', // 错误色
  info: '#3b82f6', // 信息色

  // 文字色彩
  text: {
    primary: '#ffffff', // 主文字
    secondary: '#a3a3a3', // 次要文字
    tertiary: '#666666', // 辅助文字
    placeholder: '#555555', // 占位符
  },

  // 状态色
  disabled: {
    background: '#1a1a1a',
    text: '#555555',
    opacity: 0.6,
  },
};

// 字体系统
export const typography = {
  h1: {
    fontSize: '28px',
    fontWeight: 600,
    lineHeight: 1.2,
  },
  h2: {
    fontSize: '24px',
    fontWeight: 600,
    lineHeight: 1.3,
  },
  h3: {
    fontSize: '20px',
    fontWeight: 500,
    lineHeight: 1.4,
  },
  body: {
    fontSize: '16px',
    fontWeight: 400,
    lineHeight: 1.5,
  },
  small: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: 1.4,
  },
  caption: {
    fontSize: '12px',
    fontWeight: 400,
    lineHeight: 1.3,
  },
};

// 间距系统 (基础单位: 8px)
export const spacing = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  xxl: '48px',
};

// 组件尺寸
export const sizes = {
  button: {
    large: '48px',
    medium: '40px',
    small: '32px',
  },
  input: {
    large: '48px',
    medium: '40px',
    small: '32px',
  },
};

// 圆角系统
export const borderRadius = {
  small: '6px',
  medium: '8px',
  large: '16px',
};

// 阴影系统
export const shadows = {
  card: '0 8px 32px rgba(0, 0, 0, 0.4)',
  focus: '0 0 0 3px rgba(79, 70, 229, 0.1)',
};

// 响应式断点
export const breakpoints = {
  mobile: '768px',
  tablet: '1024px',
};

// 动画配置
export const animation = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },
  easing: {
    standard: 'cubic-bezier(0.4, 0, 0.2, 1)',
    enter: 'cubic-bezier(0, 0, 0.2, 1)',
    exit: 'cubic-bezier(0.4, 0, 1, 1)',
  },
};

// 图标尺寸
export const iconSizes = {
  small: '16px',
  medium: '20px',
  large: '24px',
};

// 完整主题对象
export const theme = {
  colors,
  typography,
  spacing,
  sizes,
  borderRadius,
  shadows,
  breakpoints,
  animation,
  iconSizes,
};
