/**
 * 全局样式组件
 * 基于UI设计规范的暗色主题样式
 */
import React from 'react';
import { colors, typography, spacing, animation } from './theme';

/**
 * 全局样式组件
 */
const GlobalStyles: React.FC = () => {
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      html, body {
        height: 100%;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
          'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial,
          sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
        font-size: ${typography.body.fontSize};
        font-weight: ${typography.body.fontWeight};
        line-height: ${typography.body.lineHeight};
        color: ${colors.text.primary};
        background-color: ${colors.background.primary};
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      #root {
        height: 100%;
      }

      /* 滚动条样式 */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      ::-webkit-scrollbar-track {
        background: ${colors.background.secondary};
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb {
        background: ${colors.border.default};
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: ${colors.border.hover};
      }

      /* 全局链接样式 */
      a {
        color: ${colors.primary};
        text-decoration: none;
        transition: color ${animation.duration.fast} ${animation.easing.standard};
      }

      a:hover {
        color: ${colors.info};
        text-decoration: none;
      }

      /* 选中文本样式 */
      ::selection {
        background-color: ${colors.primary};
        color: ${colors.text.primary};
      }

      /* 工具类 */
      .text-center { text-align: center; }
      .text-left { text-align: left; }
      .text-right { text-align: right; }
      
      .flex { display: flex; }
      .flex-center { 
        display: flex; 
        align-items: center; 
        justify-content: center; 
      }
      .flex-between { 
        display: flex; 
        align-items: center; 
        justify-content: space-between; 
      }
      .flex-column { 
        display: flex; 
        flex-direction: column; 
      }

      /* 间距工具类 */
      .mt-xs { margin-top: ${spacing.xs}; }
      .mt-sm { margin-top: ${spacing.sm}; }
      .mt-md { margin-top: ${spacing.md}; }
      .mt-lg { margin-top: ${spacing.lg}; }
      .mt-xl { margin-top: ${spacing.xl}; }

      .mb-xs { margin-bottom: ${spacing.xs}; }
      .mb-sm { margin-bottom: ${spacing.sm}; }
      .mb-md { margin-bottom: ${spacing.md}; }
      .mb-lg { margin-bottom: ${spacing.lg}; }
      .mb-xl { margin-bottom: ${spacing.xl}; }

      .p-xs { padding: ${spacing.xs}; }
      .p-sm { padding: ${spacing.sm}; }
      .p-md { padding: ${spacing.md}; }
      .p-lg { padding: ${spacing.lg}; }
      .p-xl { padding: ${spacing.xl}; }

      /* 文字工具类 */
      .text-primary { color: ${colors.text.primary}; }
      .text-secondary { color: ${colors.text.secondary}; }
      .text-tertiary { color: ${colors.text.tertiary}; }

      .font-h1 { 
        font-size: ${typography.h1.fontSize}; 
        font-weight: ${typography.h1.fontWeight}; 
        line-height: ${typography.h1.lineHeight}; 
      }
      .font-h2 { 
        font-size: ${typography.h2.fontSize}; 
        font-weight: ${typography.h2.fontWeight}; 
        line-height: ${typography.h2.lineHeight}; 
      }
      .font-h3 { 
        font-size: ${typography.h3.fontSize}; 
        font-weight: ${typography.h3.fontWeight}; 
        line-height: ${typography.h3.lineHeight}; 
      }

      /* 禁用状态 */
      .disabled {
        opacity: ${colors.disabled.opacity};
        pointer-events: none;
        cursor: not-allowed;
      }

      /* 响应式隐藏 */
      @media (max-width: 767px) {
        .hidden-mobile { display: none !important; }
      }

      @media (min-width: 768px) and (max-width: 1023px) {
        .hidden-tablet { display: none !important; }
      }

      @media (min-width: 1024px) {
        .hidden-desktop { display: none !important; }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return null;
};

export default GlobalStyles;
