/**
 * 日志工具
 * 在生产环境中禁用console输出，避免构建错误
 */

const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * 开发环境日志工具
 */
export const logger = {
  /**
   * 普通日志
   */
  log: (...args: unknown[]) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.log(...args);
    }
  },

  /**
   * 信息日志
   */
  info: (...args: unknown[]) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.info(...args);
    }
  },

  /**
   * 警告日志
   */
  warn: (...args: unknown[]) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.warn(...args);
    }
  },

  /**
   * 错误日志
   */
  error: (...args: unknown[]) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.error(...args);
    }
  },

  /**
   * 调试日志
   */
  debug: (...args: unknown[]) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.debug(...args);
    }
  },
};

export default logger;
