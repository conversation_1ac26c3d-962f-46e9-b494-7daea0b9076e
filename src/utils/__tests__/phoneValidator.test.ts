/**
 * 手机号验证工具测试
 */
import {
  isValidPhone,
  formatPhone,
  getPhoneCarrier,
  cleanPhone,
  limitPhoneLength,
  validatePhone,
  maskPhone,
} from '../phoneValidator';

describe('phoneValidator', () => {
  describe('isValidPhone', () => {
    it('应该验证有效的手机号', () => {
      expect(isValidPhone('13800138000')).toBe(true);
      expect(isValidPhone('15912345678')).toBe(true);
      expect(isValidPhone('18612345678')).toBe(true);
    });

    it('应该拒绝无效的手机号', () => {
      expect(isValidPhone('12345678901')).toBe(false); // 以1开头但第二位不是3-9
      expect(isValidPhone('1380013800')).toBe(false); // 少于11位
      expect(isValidPhone('138001380000')).toBe(false); // 多于11位
      expect(isValidPhone('abc')).toBe(false); // 非数字
      expect(isValidPhone('')).toBe(false); // 空字符串
    });
  });

  describe('formatPhone', () => {
    it('应该正确格式化手机号', () => {
      expect(formatPhone('13800138000')).toBe('138 0013 8000');
      expect(formatPhone('15912345678')).toBe('159 1234 5678');
    });

    it('应该处理非11位数字', () => {
      expect(formatPhone('1380013800')).toBe('1380013800'); // 不足11位
      expect(formatPhone('138001380000')).toBe('138001380000'); // 超过11位
    });

    it('应该处理空字符串', () => {
      expect(formatPhone('')).toBe('');
    });

    it('应该清理非数字字符', () => {
      expect(formatPhone('138-0013-8000')).toBe('138 0013 8000');
      expect(formatPhone('138 0013 8000')).toBe('138 0013 8000');
    });
  });

  describe('getPhoneCarrier', () => {
    it('应该识别中国移动号码', () => {
      expect(getPhoneCarrier('13800138000')).toBe('中国移动');
      expect(getPhoneCarrier('15012345678')).toBe('中国移动');
      expect(getPhoneCarrier('18712345678')).toBe('中国移动');
    });

    it('应该识别中国联通号码', () => {
      expect(getPhoneCarrier('13012345678')).toBe('中国联通');
      expect(getPhoneCarrier('15512345678')).toBe('中国联通');
      expect(getPhoneCarrier('18612345678')).toBe('中国联通');
    });

    it('应该识别中国电信号码', () => {
      expect(getPhoneCarrier('13312345678')).toBe('中国电信');
      expect(getPhoneCarrier('15312345678')).toBe('中国电信');
      expect(getPhoneCarrier('18912345678')).toBe('中国电信');
    });

    it('应该处理未知号码段', () => {
      expect(getPhoneCarrier('12312345678')).toBe('未知运营商');
      expect(getPhoneCarrier('abc')).toBe('未知运营商');
      expect(getPhoneCarrier('')).toBe('未知运营商');
    });
  });

  describe('cleanPhone', () => {
    it('应该移除所有非数字字符', () => {
      expect(cleanPhone('138-0013-8000')).toBe('13800138000');
      expect(cleanPhone('138 0013 8000')).toBe('13800138000');
      expect(cleanPhone('(138) 0013-8000')).toBe('13800138000');
      expect(cleanPhone('abc138def0013ghi8000')).toBe('13800138000');
    });

    it('应该处理空字符串', () => {
      expect(cleanPhone('')).toBe('');
    });

    it('应该处理只有非数字字符的字符串', () => {
      expect(cleanPhone('abc-def')).toBe('');
    });
  });

  describe('limitPhoneLength', () => {
    it('应该限制手机号长度为11位', () => {
      expect(limitPhoneLength('138001380001234')).toBe('13800138000');
      expect(limitPhoneLength('1380013800')).toBe('1380013800');
    });

    it('应该支持自定义最大长度', () => {
      expect(limitPhoneLength('1234567890', 5)).toBe('12345');
    });

    it('应该清理非数字字符并限制长度', () => {
      expect(limitPhoneLength('138-0013-8000-1234')).toBe('13800138000');
    });
  });

  describe('validatePhone', () => {
    it('应该返回完整的验证结果', () => {
      const result = validatePhone('13800138000');
      expect(result).toEqual({
        isValid: true,
        formatted: '138 0013 8000',
        carrier: '中国移动',
      });
    });

    it('应该处理无效手机号', () => {
      const result = validatePhone('123456');
      expect(result.isValid).toBe(false);
      expect(result.formatted).toBe('123456');
      expect(result.carrier).toBe('未知运营商');
    });

    it('应该清理输入并验证', () => {
      const result = validatePhone('138-0013-8000');
      expect(result).toEqual({
        isValid: true,
        formatted: '138 0013 8000',
        carrier: '中国移动',
      });
    });
  });

  describe('maskPhone', () => {
    it('应该正确掩码手机号', () => {
      expect(maskPhone('13800138000')).toBe('138****8000');
      expect(maskPhone('15912345678')).toBe('159****5678');
    });

    it('应该处理非11位数字', () => {
      expect(maskPhone('1380013800')).toBe('1380013800'); // 不处理非11位
      expect(maskPhone('138001380000')).toBe('138001380000');
    });

    it('应该处理空字符串', () => {
      expect(maskPhone('')).toBe('');
    });

    it('应该清理非数字字符后掩码', () => {
      expect(maskPhone('138-0013-8000')).toBe('138****8000');
    });
  });
});
