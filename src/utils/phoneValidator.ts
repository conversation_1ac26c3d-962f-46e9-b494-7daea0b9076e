/**
 * 手机号验证工具函数
 */
import { VALIDATION, PHONE_CARRIERS } from '@constants/index';
import type { PhoneValidationResult } from '@/types';

/**
 * 验证手机号格式是否正确
 * @param phone 手机号
 * @returns 是否有效
 */
export const isValidPhone = (phone: string): boolean => {
  return VALIDATION.PHONE_PATTERN.test(phone);
};

/**
 * 格式化手机号显示
 * @param phone 手机号
 * @returns 格式化后的手机号 (例: 138 0013 8000)
 */
export const formatPhone = (phone: string): string => {
  if (!phone) return '';

  // 移除所有非数字字符
  const cleanPhone = phone.replace(/\D/g, '');

  // 格式化为 XXX XXXX XXXX
  if (cleanPhone.length === 11) {
    return cleanPhone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
  }

  return cleanPhone;
};

/**
 * 获取手机号运营商
 * @param phone 手机号
 * @returns 运营商名称
 */
export const getPhoneCarrier = (phone: string): string => {
  if (!phone || phone.length < 3) return '未知运营商';

  const prefix = phone.substring(0, 3);
  return PHONE_CARRIERS[prefix as keyof typeof PHONE_CARRIERS] || '未知运营商';
};

/**
 * 清理手机号（只保留数字）
 * @param phone 输入的手机号
 * @returns 清理后的数字字符串
 */
export const cleanPhone = (phone: string): string => {
  return phone.replace(/\D/g, '');
};

/**
 * 限制手机号输入长度
 * @param phone 输入的手机号
 * @param maxLength 最大长度，默认11位
 * @returns 限制长度后的手机号
 */
export const limitPhoneLength = (phone: string, maxLength = 11): string => {
  const cleaned = cleanPhone(phone);
  return cleaned.substring(0, maxLength);
};

/**
 * 验证手机号并返回详细信息
 * @param phone 手机号
 * @returns 验证结果对象
 */
export const validatePhone = (phone: string): PhoneValidationResult => {
  const cleaned = cleanPhone(phone);

  return {
    isValid: isValidPhone(cleaned),
    formatted: formatPhone(cleaned),
    carrier: getPhoneCarrier(cleaned),
  };
};

/**
 * 掩码显示手机号
 * @param phone 手机号
 * @returns 掩码后的手机号 (例: 138****8000)
 */
export const maskPhone = (phone: string): string => {
  if (!phone) return '';

  const cleaned = cleanPhone(phone);
  if (cleaned.length !== 11) return phone;

  return cleaned.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};
