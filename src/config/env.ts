/**
 * 环境变量配置管理模块
 * 用于加载和管理不同环境下的配置参数
 */

import { logger } from '@/utils/logger';

/**
 * 环境类型枚举
 */
export enum Environment {
  DEVELOPMENT = 'development',
  PRODUCTION = 'production',
  TEST = 'test',
}

/**
 * 应用配置接口
 */
export interface AppConfig {
  // 应用基础信息
  name: string;
  version: string;
  description: string;
  environment: Environment;

  // API 配置
  api: {
    baseUrl: string;
    version: string;
    timeout: number;
  };

  // 认证配置
  auth: {
    jwtSecretKey: string;
    jwtExpiresIn: string;
    refreshTokenExpiresIn: string;
  };

  // 功能开关配置
  features: {
    enableMockData: boolean;
    enableDebugMode: boolean;
    enableHotReload: boolean;
  };

  // 主题配置
  theme: {
    defaultTheme: string;
    primaryColor: string;
    successColor: string;
    warningColor: string;
    errorColor: string;
  };

  // 分页配置
  pagination: {
    defaultPageSize: number;
    maxPageSize: number;
  };
}

/**
 * 获取环境变量值，支持默认值
 * @param key 环境变量名
 * @param defaultValue 默认值
 * @returns 环境变量值或默认值
 */
const getEnvVar = (key: string, defaultValue = ''): string => {
  return process.env[key] || defaultValue;
};

/**
 * 获取布尔类型环境变量
 * @param key 环境变量名
 * @param defaultValue 默认值
 * @returns 布尔值
 */
const getBooleanEnvVar = (key: string, defaultValue = false): boolean => {
  const value = getEnvVar(key);
  return value ? value.toLowerCase() === 'true' : defaultValue;
};

/**
 * 获取数字类型环境变量
 * @param key 环境变量名
 * @param defaultValue 默认值
 * @returns 数字值
 */
const getNumberEnvVar = (key: string, defaultValue = 0): number => {
  const value = getEnvVar(key);
  return value ? parseInt(value, 10) : defaultValue;
};

/**
 * 获取数组类型环境变量（暂未使用）
 * @param key 环境变量名
 * @param separator 分隔符
 * @param defaultValue 默认值
 * @returns 数组值
 */
// const getArrayEnvVar = (key: string, separator = ',', defaultValue: string[] = []): string[] => {
//   const value = getEnvVar(key);
//   return value ? value.split(separator).map(item => item.trim()) : defaultValue;
// };

/**
 * 当前环境
 */
export const currentEnvironment = getEnvVar(
  'REACT_APP_ENV',
  Environment.DEVELOPMENT
) as Environment;

/**
 * 是否为开发环境
 */
export const isDevelopment = currentEnvironment === Environment.DEVELOPMENT;

/**
 * 是否为生产环境
 */
export const isProduction = currentEnvironment === Environment.PRODUCTION;

/**
 * 是否为测试环境
 */
export const isTest = currentEnvironment === Environment.TEST;

/**
 * 应用配置对象
 */
export const config: AppConfig = {
  // 应用基础信息
  name: getEnvVar('REACT_APP_NAME', 'FinSight'),
  version: getEnvVar('REACT_APP_VERSION', '0.1.0'),
  description: getEnvVar('REACT_APP_DESCRIPTION', '信息降噪，即时触达'),
  environment: currentEnvironment,

  // API 配置
  api: {
    baseUrl: getEnvVar('REACT_APP_API_BASE_URL', ''),
    version: getEnvVar('REACT_APP_API_VERSION', 'v1'),
    timeout: getNumberEnvVar('REACT_APP_API_TIMEOUT', 30000),
  },

  // 认证配置
  auth: {
    jwtSecretKey: getEnvVar('REACT_APP_JWT_SECRET_KEY', 'default-jwt-secret'),
    jwtExpiresIn: getEnvVar('REACT_APP_JWT_EXPIRES_IN', '7d'),
    refreshTokenExpiresIn: getEnvVar(
      'REACT_APP_REFRESH_TOKEN_EXPIRES_IN',
      '7d'
    ),
  },

  // // 第三方服务配置
  // services: {
  //   googleAnalyticsId: getEnvVar('REACT_APP_GOOGLE_ANALYTICS_ID'),
  //   sentryDsn: getEnvVar('REACT_APP_SENTRY_DSN'),
  // },

  // 功能开关配置
  features: {
    enableMockData: getBooleanEnvVar(
      'REACT_APP_ENABLE_MOCK_DATA',
      isDevelopment
    ),
    enableDebugMode: getBooleanEnvVar(
      'REACT_APP_ENABLE_DEBUG_MODE',
      isDevelopment
    ),
    enableHotReload: getBooleanEnvVar(
      'REACT_APP_ENABLE_HOT_RELOAD',
      isDevelopment
    ),
  },

  // // 上传配置
  // upload: {
  //   maxFileSize: getNumberEnvVar('REACT_APP_MAX_FILE_SIZE', 10485760), // 10MB
  //   allowedFileTypes: getArrayEnvVar('REACT_APP_ALLOWED_FILE_TYPES', ',', [
  //     'image/jpeg',
  //     'image/png',
  //     'image/gif',
  //     'application/pdf'
  //   ]),
  // },

  // // 缓存配置
  // cache: {
  //   ttl: getNumberEnvVar('REACT_APP_CACHE_TTL', 300000), // 5分钟
  //   localStoragePrefix: getEnvVar('REACT_APP_LOCAL_STORAGE_PREFIX', 'finsight_'),
  // },

  // 主题配置
  theme: {
    defaultTheme: getEnvVar('REACT_APP_DEFAULT_THEME', 'light'),
    primaryColor: getEnvVar('REACT_APP_PRIMARY_COLOR', '#1890ff'),
    successColor: getEnvVar('REACT_APP_SUCCESS_COLOR', '#52c41a'),
    warningColor: getEnvVar('REACT_APP_WARNING_COLOR', '#faad14'),
    errorColor: getEnvVar('REACT_APP_ERROR_COLOR', '#f5222d'),
  },

  // 分页配置
  pagination: {
    defaultPageSize: getNumberEnvVar('REACT_APP_DEFAULT_PAGE_SIZE', 20),
    maxPageSize: getNumberEnvVar('REACT_APP_MAX_PAGE_SIZE', 100),
  },
};

/**
 * 配置验证函数
 * 验证必需的配置项是否存在
 */
export const validateConfig = (): void => {
  const requiredFields = ['api.baseUrl', 'auth.jwtSecretKey'];

  const errors: string[] = [];

  requiredFields.forEach(field => {
    const keys = field.split('.');
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let value: any = config;

    for (const key of keys) {
      value = value[key];
      if (value === undefined || value === null || value === '') {
        errors.push(`Missing required configuration: ${field}`);
        break;
      }
    }
  });

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
};

/**
 * 打印配置信息（开发环境）
 */
export const printConfig = (): void => {
  if (isDevelopment && config.features.enableDebugMode) {
    logger.log('🔧 Application Configuration');
    logger.log('Environment:', config.environment);
    logger.log('API Base URL:', config.api.baseUrl);
    logger.log('Features:', config.features);
    logger.log('Theme:', config.theme);
  }
};

// 在模块加载时验证配置
try {
  validateConfig();
  printConfig();
} catch (error) {
  logger.error('❌ Configuration Error:', error);
  if (isProduction) {
    throw error;
  }
}
