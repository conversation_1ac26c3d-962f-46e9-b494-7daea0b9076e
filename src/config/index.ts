/**
 * 配置模块统一导出
 * 提供项目所有配置相关的导出
 */

// 导出环境配置
export {
  config,
  Environment,
  currentEnvironment,
  isDevelopment,
  isProduction,
  isTest,
  validateConfig,
  printConfig,
  type AppConfig,
} from './env';

// 导出环境变量类型定义
export { ENV_KEYS, ENV_VALIDATION_RULES } from '@/types/env';

// 导出配置相关的 Hooks
export {
  useConfig,
  useEnvironment,
  useFeatureFlags,
  useApiConfig,
  useThemeConfig,
} from '@/hooks/useConfig';
