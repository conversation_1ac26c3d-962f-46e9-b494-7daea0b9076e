/**
 * 环境变量类型定义
 * 扩展 NodeJS.ProcessEnv 接口，为所有环境变量提供类型安全
 */

declare namespace NodeJS {
  interface ProcessEnv {
    // 基础环境变量
    readonly NODE_ENV: 'development' | 'production' | 'test';

    // 应用配置
    readonly REACT_APP_NAME?: string;
    readonly REACT_APP_VERSION?: string;
    readonly REACT_APP_DESCRIPTION?: string;
    readonly REACT_APP_ENV?: 'development' | 'production' | 'test';

    // API 配置
    readonly REACT_APP_API_BASE_URL?: string;
    readonly REACT_APP_API_VERSION?: string;
    readonly REACT_APP_API_TIMEOUT?: string;

    // 认证配置
    readonly REACT_APP_JWT_SECRET_KEY?: string;
    readonly REACT_APP_JWT_EXPIRES_IN?: string;
    readonly REACT_APP_REFRESH_TOKEN_EXPIRES_IN?: string;

    // 第三方服务配置
    readonly REACT_APP_GOOGLE_ANALYTICS_ID?: string;
    readonly REACT_APP_SENTRY_DSN?: string;

    // 功能开关
    readonly REACT_APP_ENABLE_MOCK_DATA?: string;
    readonly REACT_APP_ENABLE_DEBUG_MODE?: string;
    readonly REACT_APP_ENABLE_HOT_RELOAD?: string;

    // 上传配置
    readonly REACT_APP_MAX_FILE_SIZE?: string;
    readonly REACT_APP_ALLOWED_FILE_TYPES?: string;

    // 缓存配置
    readonly REACT_APP_CACHE_TTL?: string;
    readonly REACT_APP_LOCAL_STORAGE_PREFIX?: string;

    // WebSocket 配置
    readonly REACT_APP_WS_URL?: string;
    readonly REACT_APP_WS_RECONNECT_INTERVAL?: string;

    // 主题配置
    readonly REACT_APP_DEFAULT_THEME?: string;
    readonly REACT_APP_PRIMARY_COLOR?: string;
    readonly REACT_APP_SUCCESS_COLOR?: string;
    readonly REACT_APP_WARNING_COLOR?: string;
    readonly REACT_APP_ERROR_COLOR?: string;

    // 分页配置
    readonly REACT_APP_DEFAULT_PAGE_SIZE?: string;
    readonly REACT_APP_MAX_PAGE_SIZE?: string;

    // 地图配置
    readonly REACT_APP_MAP_API_KEY?: string;
    readonly REACT_APP_MAP_DEFAULT_CENTER_LAT?: string;
    readonly REACT_APP_MAP_DEFAULT_CENTER_LNG?: string;

    // CDN 配置
    readonly REACT_APP_CDN_BASE_URL?: string;
    readonly REACT_APP_STATIC_ASSETS_URL?: string;
  }
}

/**
 * 环境变量键名常量
 */
export const ENV_KEYS = {
  // 应用配置
  APP_NAME: 'REACT_APP_NAME',
  APP_VERSION: 'REACT_APP_VERSION',
  APP_DESCRIPTION: 'REACT_APP_DESCRIPTION',
  APP_ENV: 'REACT_APP_ENV',

  // API 配置
  API_BASE_URL: 'REACT_APP_API_BASE_URL',
  API_VERSION: 'REACT_APP_API_VERSION',
  API_TIMEOUT: 'REACT_APP_API_TIMEOUT',

  // 认证配置
  JWT_SECRET_KEY: 'REACT_APP_JWT_SECRET_KEY',
  JWT_EXPIRES_IN: 'REACT_APP_JWT_EXPIRES_IN',
  REFRESH_TOKEN_EXPIRES_IN: 'REACT_APP_REFRESH_TOKEN_EXPIRES_IN',

  // 第三方服务配置
  GOOGLE_ANALYTICS_ID: 'REACT_APP_GOOGLE_ANALYTICS_ID',
  SENTRY_DSN: 'REACT_APP_SENTRY_DSN',

  // 功能开关
  ENABLE_MOCK_DATA: 'REACT_APP_ENABLE_MOCK_DATA',
  ENABLE_DEBUG_MODE: 'REACT_APP_ENABLE_DEBUG_MODE',
  ENABLE_HOT_RELOAD: 'REACT_APP_ENABLE_HOT_RELOAD',

  // 上传配置
  MAX_FILE_SIZE: 'REACT_APP_MAX_FILE_SIZE',
  ALLOWED_FILE_TYPES: 'REACT_APP_ALLOWED_FILE_TYPES',

  // 缓存配置
  CACHE_TTL: 'REACT_APP_CACHE_TTL',
  LOCAL_STORAGE_PREFIX: 'REACT_APP_LOCAL_STORAGE_PREFIX',

  // WebSocket 配置
  WS_URL: 'REACT_APP_WS_URL',
  WS_RECONNECT_INTERVAL: 'REACT_APP_WS_RECONNECT_INTERVAL',

  // 主题配置
  DEFAULT_THEME: 'REACT_APP_DEFAULT_THEME',
  PRIMARY_COLOR: 'REACT_APP_PRIMARY_COLOR',
  SUCCESS_COLOR: 'REACT_APP_SUCCESS_COLOR',
  WARNING_COLOR: 'REACT_APP_WARNING_COLOR',
  ERROR_COLOR: 'REACT_APP_ERROR_COLOR',

  // 分页配置
  DEFAULT_PAGE_SIZE: 'REACT_APP_DEFAULT_PAGE_SIZE',
  MAX_PAGE_SIZE: 'REACT_APP_MAX_PAGE_SIZE',

  // 地图配置
  MAP_API_KEY: 'REACT_APP_MAP_API_KEY',
  MAP_DEFAULT_CENTER_LAT: 'REACT_APP_MAP_DEFAULT_CENTER_LAT',
  MAP_DEFAULT_CENTER_LNG: 'REACT_APP_MAP_DEFAULT_CENTER_LNG',

  // CDN 配置
  CDN_BASE_URL: 'REACT_APP_CDN_BASE_URL',
  STATIC_ASSETS_URL: 'REACT_APP_STATIC_ASSETS_URL',
} as const;

/**
 * 环境变量验证规则
 */
export const ENV_VALIDATION_RULES = {
  required: ['REACT_APP_API_BASE_URL', 'REACT_APP_JWT_SECRET_KEY'],
  optional: [
    'REACT_APP_NAME',
    'REACT_APP_VERSION',
    'REACT_APP_DESCRIPTION',
    'REACT_APP_GOOGLE_ANALYTICS_ID',
    'REACT_APP_SENTRY_DSN',
    'REACT_APP_MAP_API_KEY',
  ],
  boolean: [
    'REACT_APP_ENABLE_MOCK_DATA',
    'REACT_APP_ENABLE_DEBUG_MODE',
    'REACT_APP_ENABLE_HOT_RELOAD',
  ],
  number: [
    'REACT_APP_API_TIMEOUT',
    'REACT_APP_MAX_FILE_SIZE',
    'REACT_APP_CACHE_TTL',
    'REACT_APP_WS_RECONNECT_INTERVAL',
    'REACT_APP_DEFAULT_PAGE_SIZE',
    'REACT_APP_MAX_PAGE_SIZE',
    'REACT_APP_MAP_DEFAULT_CENTER_LAT',
    'REACT_APP_MAP_DEFAULT_CENTER_LNG',
  ],
  url: [
    'REACT_APP_API_BASE_URL',
    'REACT_APP_SENTRY_DSN',
    'REACT_APP_WS_URL',
    'REACT_APP_CDN_BASE_URL',
    'REACT_APP_STATIC_ASSETS_URL',
  ],
} as const;
