import React from 'react';

// 基础接口
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// API 响应结构
export interface ApiResponse<T = unknown> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 用户相关类型
export interface User extends BaseEntity {
  username: string;
  email: string;
  avatar?: string;
  role: UserRole;
  status: UserStatus;
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
}

// 通用组件 Props 类型
export interface CommonProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

// 异步状态类型
export interface AsyncState<T = unknown> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

// 认证相关类型定义
export interface AuthUser {
  id: number;
  phone: string;
  username?: string | null;
  email?: string | null;
  user_type: number; // 1:小白型, 2:进阶型, 3:焦虑型
  risk_level: number; // 风险等级（1-5）
  knowledge_level: number; // 知识水平（1-5）
  is_active: boolean;
  is_verified: boolean;
  first_login_at: string;
  last_login_at: string;
  created_at: string;
  updated_at: string;
}

// 手机号登录请求参数
export interface PhoneLoginRequest {
  phone: string;
  verification_code: string;
}

// 发送短信验证码请求参数
export interface SendSmsCodeRequest {
  phone: string;
  purpose?: string;
}

// 登录响应
export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: AuthUser;
}

// 发送验证码响应
export interface SendSmsCodeResponse {
  message: string;
  expires_in: number;
}

// 刷新token请求
export interface RefreshTokenRequest {
  refresh_token: string;
}

// 刷新token响应
export interface RefreshTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

// 认证状态
export interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  accessToken: string | null;
  refreshToken: string | null;
  tokenExpiresAt: number | null;
  loading: boolean;
  error: string | null;
}

// API错误类型
export class ApiError extends Error {
  status: number;
  code?: string;

  constructor(message: string, status: number, code?: string) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
  }
}

// 手机号验证结果
export interface PhoneValidationResult {
  isValid: boolean;
  formatted: string;
  carrier: string;
}

// 组件Props类型
export interface PhoneAuthFormProps {
  onSuccess?: (response: LoginResponse) => void;
  onError?: (error: ApiError) => void;
}

export interface SmsCodeInputProps {
  value: string;
  onChange: (value: string) => void;
  onComplete?: (value: string) => void;
  disabled?: boolean;
  autoFocus?: boolean;
}
