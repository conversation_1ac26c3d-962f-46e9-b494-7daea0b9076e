/**
 * Redux Store 配置
 */
import { configureStore } from '@reduxjs/toolkit';

import authSlice from './slices/authSlice';
import appSlice from './slices/appSlice';

// 配置 Redux Store
export const store = configureStore({
  reducer: {
    auth: authSlice,
    app: appSlice,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// 推导出类型
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
