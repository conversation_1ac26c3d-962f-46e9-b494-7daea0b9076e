/**
 * 认证相关的 Redux slice
 */
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';

import type {
  AuthUser,
  AuthState,
  LoginResponse,
  SendSmsCodeRequest,
  PhoneLoginRequest,
} from '@/types';
import { ApiError } from '@/types';
import { authService } from '@services/authService';
import { TokenManager } from '@services/api';
import { logger } from '@/utils/logger';
import { STORAGE_KEYS } from '@constants/index';

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  accessToken: null,
  refreshToken: null,
  tokenExpiresAt: null,
  loading: false,
  error: null,
};

// 异步actions
export const sendSmsCode = createAsyncThunk(
  'auth/sendSmsCode',
  async (params: SendSmsCodeRequest, { rejectWithValue }) => {
    try {
      return await authService.sendSmsCode(params);
    } catch (error) {
      return rejectWithValue(
        error instanceof ApiError ? error.message : '发送验证码失败'
      );
    }
  }
);

export const phoneLogin = createAsyncThunk(
  'auth/phoneLogin',
  async (params: PhoneLoginRequest, { rejectWithValue }) => {
    try {
      const response = await authService.phoneLogin(params);

      // 保存token到localStorage
      TokenManager.setTokens(
        response.access_token,
        response.refresh_token,
        response.expires_in
      );

      // 保存用户信息
      localStorage.setItem(
        STORAGE_KEYS.USER_INFO,
        JSON.stringify(response.user)
      );

      return response;
    } catch (error) {
      return rejectWithValue(
        error instanceof ApiError ? error.message : '登录失败'
      );
    }
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const refreshToken = TokenManager.getRefreshToken();
      if (!refreshToken) {
        throw new Error('无刷新令牌');
      }

      const response = await authService.refreshToken({
        refresh_token: refreshToken,
      });

      // 更新token
      const expiresAt = Date.now() + response.expires_in * 1000;
      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, response.access_token);
      localStorage.setItem(STORAGE_KEYS.TOKEN_EXPIRES_AT, expiresAt.toString());

      return {
        accessToken: response.access_token,
        expiresAt,
      };
    } catch (error) {
      TokenManager.clearTokens();
      return rejectWithValue(
        error instanceof ApiError ? error.message : '刷新令牌失败'
      );
    }
  }
);

export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      return await authService.getCurrentUser();
    } catch (error) {
      return rejectWithValue(
        error instanceof ApiError ? error.message : '获取用户信息失败'
      );
    }
  }
);

export const logout = createAsyncThunk('auth/logout', async () => {
  try {
    await authService.logout();
  } catch (error) {
    // 即使服务端登出失败，也要清理本地状态
    logger.warn('服务端登出失败:', error);
  } finally {
    TokenManager.clearTokens();
  }
});

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // 清除错误
    clearError: state => {
      state.error = null;
    },

    // 更新用户信息
    updateUser: (state, action: PayloadAction<Partial<AuthUser>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
        localStorage.setItem(
          STORAGE_KEYS.USER_INFO,
          JSON.stringify(state.user)
        );
      }
    },

    // 从本地存储恢复认证状态
    restoreAuthState: state => {
      const accessToken = TokenManager.getAccessToken();
      const refreshToken = TokenManager.getRefreshToken();
      const tokenExpiresAt = TokenManager.getTokenExpiresAt();
      const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO);

      if (accessToken && refreshToken && userInfo) {
        try {
          state.accessToken = accessToken;
          state.refreshToken = refreshToken;
          state.tokenExpiresAt = tokenExpiresAt;
          state.user = JSON.parse(userInfo);
          state.isAuthenticated = true;
        } catch (error) {
          // 解析失败，清除无效数据
          TokenManager.clearTokens();
        }
      }
    },

    // 重置认证状态
    resetAuthState: state => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: builder => {
    // 发送短信验证码
    builder
      .addCase(sendSmsCode.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(sendSmsCode.fulfilled, state => {
        state.loading = false;
      })
      .addCase(sendSmsCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 手机号登录
    builder
      .addCase(phoneLogin.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        phoneLogin.fulfilled,
        (state, action: PayloadAction<LoginResponse>) => {
          state.loading = false;
          state.isAuthenticated = true;
          state.user = action.payload.user;
          state.accessToken = action.payload.access_token;
          state.refreshToken = action.payload.refresh_token;
          state.tokenExpiresAt = Date.now() + action.payload.expires_in * 1000;
          state.error = null;
        }
      )
      .addCase(phoneLogin.rejected, (state, action) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.tokenExpiresAt = null;
        state.error = action.payload as string;
      });

    // 刷新token
    builder
      .addCase(refreshToken.pending, state => {
        state.loading = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.loading = false;
        state.accessToken = action.payload.accessToken;
        state.tokenExpiresAt = action.payload.expiresAt;
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.tokenExpiresAt = null;
        state.error = action.payload as string;
      });

    // 获取当前用户
    builder
      .addCase(getCurrentUser.pending, state => {
        state.loading = true;
      })
      .addCase(
        getCurrentUser.fulfilled,
        (state, action: PayloadAction<AuthUser>) => {
          state.loading = false;
          state.user = action.payload;
          localStorage.setItem(
            STORAGE_KEYS.USER_INFO,
            JSON.stringify(action.payload)
          );
        }
      )
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 登出
    builder.addCase(logout.fulfilled, state => {
      Object.assign(state, initialState);
    });
  },
});

export const { clearError, updateUser, restoreAuthState, resetAuthState } =
  authSlice.actions;

export default authSlice.reducer;
