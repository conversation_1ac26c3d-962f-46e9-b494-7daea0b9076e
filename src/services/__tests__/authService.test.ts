/**
 * 认证服务测试
 */
import { AuthService } from '../authService';
import apiClient from '../api';
import { ApiError } from '@/types';
import { logger } from '@/utils/logger';

// Mock apiClient
jest.mock('../api');
const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Mock logger
jest.mock('@/utils/logger');
const mockedLogger = logger as jest.Mocked<typeof logger>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('sendSmsCode', () => {
    it('应该成功发送短信验证码', async () => {
      const mockResponse = {
        data: {
          message: 'Verification code sent successfully',
          expires_in: 300,
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const params = { phone: '13800138000' };
      const result = await AuthService.sendSmsCode(params);

      expect(mockedApiClient.post).toHaveBeenCalledWith(
        '/users/send-sms-code',
        params
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('应该处理发送短信验证码的错误', async () => {
      const mockError = {
        response: {
          status: 429,
          data: { detail: '发送频率过高' },
        },
      };

      mockedApiClient.post.mockRejectedValue(mockError);

      const params = { phone: '13800138000' };

      await expect(AuthService.sendSmsCode(params)).rejects.toThrow(ApiError);
    });
  });

  describe('phoneLogin', () => {
    it('应该成功进行手机号登录', async () => {
      const mockResponse = {
        data: {
          access_token: 'mock-access-token',
          refresh_token: 'mock-refresh-token',
          token_type: 'bearer',
          expires_in: 1800,
          user: {
            id: 1,
            phone: '13800138000',
            username: null,
            email: null,
            user_type: 1,
            risk_level: 3,
            knowledge_level: 1,
            is_active: true,
            is_verified: true,
            first_login_at: '2024-01-01T10:00:00Z',
            last_login_at: '2024-01-01T10:00:00Z',
            created_at: '2024-01-01T10:00:00Z',
            updated_at: '2024-01-01T10:00:00Z',
          },
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const params = {
        phone: '13800138000',
        verification_code: '123456',
      };
      const result = await AuthService.phoneLogin(params);

      expect(mockedApiClient.post).toHaveBeenCalledWith(
        '/users/phone-login',
        params
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('应该处理登录错误', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { detail: '验证码错误或已过期' },
        },
      };

      mockedApiClient.post.mockRejectedValue(mockError);

      const params = {
        phone: '13800138000',
        verification_code: '123456',
      };

      await expect(AuthService.phoneLogin(params)).rejects.toThrow(ApiError);
    });
  });

  describe('refreshToken', () => {
    it('应该成功刷新访问令牌', async () => {
      const mockResponse = {
        data: {
          access_token: 'new-access-token',
          token_type: 'bearer',
          expires_in: 1800,
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const params = { refresh_token: 'mock-refresh-token' };
      const result = await AuthService.refreshToken(params);

      expect(mockedApiClient.post).toHaveBeenCalledWith(
        '/users/refresh-token',
        params
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('应该处理刷新令牌错误', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { detail: '刷新令牌无效或已过期' },
        },
      };

      mockedApiClient.post.mockRejectedValue(mockError);

      const params = { refresh_token: 'invalid-refresh-token' };

      await expect(AuthService.refreshToken(params)).rejects.toThrow(ApiError);
    });
  });

  describe('getCurrentUser', () => {
    it('应该成功获取当前用户信息', async () => {
      const mockResponse = {
        data: {
          id: 1,
          phone: '13800138000',
          username: '测试用户',
          email: '<EMAIL>',
          user_type: 1,
          risk_level: 3,
          knowledge_level: 2,
          is_active: true,
          is_verified: true,
          first_login_at: '2024-01-01T10:00:00Z',
          last_login_at: '2024-01-01T12:00:00Z',
          created_at: '2024-01-01T10:00:00Z',
          updated_at: '2024-01-01T11:30:00Z',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await AuthService.getCurrentUser();

      expect(mockedApiClient.get).toHaveBeenCalledWith('/users/me');
      expect(result).toEqual(mockResponse.data);
    });

    it('应该处理获取用户信息的错误', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { detail: '未授权' },
        },
      };

      mockedApiClient.get.mockRejectedValue(mockError);

      await expect(AuthService.getCurrentUser()).rejects.toThrow(ApiError);
    });
  });

  describe('updateProfile', () => {
    it('应该成功更新用户资料', async () => {
      const mockResponse = {
        data: {
          id: 1,
          phone: '13800138000',
          username: '新用户名',
          email: '<EMAIL>',
          user_type: 2,
          risk_level: 4,
          knowledge_level: 3,
          is_active: true,
          is_verified: true,
          first_login_at: '2024-01-01T10:00:00Z',
          last_login_at: '2024-01-01T12:00:00Z',
          created_at: '2024-01-01T10:00:00Z',
          updated_at: '2024-01-01T13:00:00Z',
        },
      };

      mockedApiClient.put.mockResolvedValue(mockResponse);

      const updateData = {
        username: '新用户名',
        email: '<EMAIL>',
        user_type: 2,
      };
      const result = await AuthService.updateProfile(updateData);

      expect(mockedApiClient.put).toHaveBeenCalledWith(
        '/users/profile',
        updateData
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('应该处理更新用户资料的错误', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { detail: '参数验证失败' },
        },
      };

      mockedApiClient.put.mockRejectedValue(mockError);

      const updateData = { username: '' }; // 无效数据

      await expect(AuthService.updateProfile(updateData)).rejects.toThrow(
        ApiError
      );
    });
  });

  describe('logout', () => {
    it('应该成功登出', async () => {
      mockedApiClient.post.mockResolvedValue({ data: {} });

      await expect(AuthService.logout()).resolves.toBeUndefined();

      expect(mockedApiClient.post).toHaveBeenCalledWith('/users/logout');
    });

    it('应该处理登出错误但不抛出异常', async () => {
      const mockError = new Error('网络错误');
      mockedApiClient.post.mockRejectedValue(mockError);

      await expect(AuthService.logout()).resolves.toBeUndefined();

      expect(mockedLogger.warn).toHaveBeenCalledWith(
        '服务端登出失败:',
        mockError
      );
    });
  });

  describe('handleError', () => {
    it('应该处理响应错误', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { detail: '请求参数错误', code: 'INVALID_PARAMS' },
        },
      };

      mockedApiClient.post.mockRejectedValue(mockError);

      try {
        await AuthService.sendSmsCode({ phone: '13800138000' });
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError);
        expect((error as ApiError).message).toBe('请求参数错误');
        expect((error as ApiError).status).toBe(400);
        expect((error as ApiError).code).toBe('INVALID_PARAMS');
      }
    });

    it('应该处理网络错误', async () => {
      const mockError = {
        request: {},
        message: 'Network Error',
      };

      mockedApiClient.post.mockRejectedValue(mockError);

      try {
        await AuthService.sendSmsCode({ phone: '13800138000' });
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError);
        expect((error as ApiError).message).toBe(
          '网络连接失败，请检查网络状态'
        );
        expect((error as ApiError).status).toBe(0);
      }
    });

    it('应该处理其他错误', async () => {
      const mockError = new Error('未知错误');

      mockedApiClient.post.mockRejectedValue(mockError);

      try {
        await AuthService.sendSmsCode({ phone: '13800138000' });
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError);
        expect((error as ApiError).message).toBe('未知错误');
        expect((error as ApiError).status).toBe(0);
      }
    });
  });
});
