/**
 * API 服务层基础配置
 */
import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';

import {
  API_BASE_URL,
  API_TIMEOUT,
  STORAGE_KEYS,
  AUTH,
} from '@constants/index';
import type { ApiResponse, RefreshTokenResponse } from '@/types';
import { ApiError } from '@/types';

// 创建 axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token 管理工具
class TokenManager {
  private static isRefreshing = false;
  private static failedQueue: Array<{
    resolve: (value: string | null) => void;
    reject: (reason: unknown) => void;
  }> = [];

  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | null {
    return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
  }

  /**
   * 获取刷新令牌
   */
  static getRefreshToken(): string | null {
    return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
  }

  /**
   * 获取令牌过期时间
   */
  static getTokenExpiresAt(): number | null {
    const expiresAt = localStorage.getItem(STORAGE_KEYS.TOKEN_EXPIRES_AT);
    return expiresAt ? parseInt(expiresAt, 10) : null;
  }

  /**
   * 设置认证信息
   */
  static setTokens(
    accessToken: string,
    refreshToken: string,
    expiresIn: number
  ): void {
    const expiresAt = Date.now() + expiresIn * 1000;

    localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    localStorage.setItem(STORAGE_KEYS.TOKEN_EXPIRES_AT, expiresAt.toString());
  }

  /**
   * 清除认证信息
   */
  static clearTokens(): void {
    localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.TOKEN_EXPIRES_AT);
    localStorage.removeItem(STORAGE_KEYS.USER_INFO);
  }

  /**
   * 检查token是否即将过期
   */
  static isTokenExpiringSoon(): boolean {
    const expiresAt = this.getTokenExpiresAt();
    if (!expiresAt) return true;

    const timeUntilExpiry = expiresAt - Date.now();
    return timeUntilExpiry < AUTH.TOKEN_REFRESH_THRESHOLD * 1000;
  }

  /**
   * 刷新访问令牌
   */
  static async refreshAccessToken(): Promise<string | null> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      this.clearTokens();
      return null;
    }

    // 防止并发刷新
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject });
      });
    }

    this.isRefreshing = true;

    try {
      const response = await axios.post(
        `${API_BASE_URL}/users/refresh-token`,
        { refresh_token: refreshToken },
        { timeout: API_TIMEOUT }
      );

      const data: RefreshTokenResponse = response.data;

      // 更新访问令牌
      const expiresAt = Date.now() + data.expires_in * 1000;
      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, data.access_token);
      localStorage.setItem(STORAGE_KEYS.TOKEN_EXPIRES_AT, expiresAt.toString());

      // 处理等待队列
      this.failedQueue.forEach(({ resolve }) => resolve(data.access_token));
      this.failedQueue = [];

      return data.access_token;
    } catch (error) {
      // 刷新失败，清除所有认证信息
      this.clearTokens();

      // 处理等待队列
      this.failedQueue.forEach(({ reject }) => reject(error));
      this.failedQueue = [];

      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }
}

// 请求拦截器
apiClient.interceptors.request.use(
  async config => {
    const accessToken = TokenManager.getAccessToken();

    if (accessToken) {
      // 检查token是否即将过期
      if (TokenManager.isTokenExpiringSoon()) {
        try {
          const newToken = await TokenManager.refreshAccessToken();
          if (newToken) {
            config.headers.Authorization = `Bearer ${newToken}`;
          }
        } catch (error) {
          // 刷新失败，清除token并继续请求
          TokenManager.clearTokens();
        }
      } else {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    return response;
  },
  async (error: AxiosError<ApiResponse>) => {
    const originalRequest = error.config as typeof error.config & {
      _retry?: boolean;
    };

    // 处理401错误（token无效或过期）
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const newToken = await TokenManager.refreshAccessToken();
        if (newToken) {
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // 刷新失败，重定向到登录页
        TokenManager.clearTokens();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // 处理其他错误
    const responseData = error.response?.data as unknown as Record<
      string,
      unknown
    >;
    const message =
      (responseData?.detail as string) ||
      (responseData?.message as string) ||
      error.message ||
      '网络错误';

    const apiError = new ApiError(
      message,
      error.response?.status || 0,
      (responseData?.code as string) || undefined
    );

    return Promise.reject(apiError);
  }
);

// 导出token管理器和API客户端
export { TokenManager };
export default apiClient;
