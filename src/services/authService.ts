/**
 * 认证服务API
 */
import apiClient from './api';
import type {
  SendSmsCodeRequest,
  SendSmsCodeResponse,
  PhoneLoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  AuthUser,
} from '@/types';
import { ApiError } from '@/types';
import { logger } from '@/utils/logger';

/**
 * 认证API服务类
 */
export class AuthService {
  /**
   * 发送短信验证码
   * @param params 请求参数
   * @returns 发送结果
   */
  static async sendSmsCode(
    params: SendSmsCodeRequest
  ): Promise<SendSmsCodeResponse> {
    try {
      const response = await apiClient.post('/users/send-sms-code', params);
      return response.data;
    } catch (error: unknown) {
      throw AuthService.handleError(error, '发送验证码失败');
    }
  }

  /**
   * 手机号验证码登录
   * @param params 登录参数
   * @returns 登录响应
   */
  static async phoneLogin(params: PhoneLoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post('/users/phone-login', params);
      return response.data;
    } catch (error: unknown) {
      throw AuthService.handleError(error, '登录失败');
    }
  }

  /**
   * 刷新访问令牌
   * @param params 刷新参数
   * @returns 新的访问令牌
   */
  static async refreshToken(
    params: RefreshTokenRequest
  ): Promise<RefreshTokenResponse> {
    try {
      const response = await apiClient.post('/users/refresh-token', params);
      return response.data;
    } catch (error: unknown) {
      throw AuthService.handleError(error, '刷新令牌失败');
    }
  }

  /**
   * 获取当前用户信息
   * @returns 用户信息
   */
  static async getCurrentUser(): Promise<AuthUser> {
    try {
      const response = await apiClient.get('/users/me');
      return response.data;
    } catch (error: unknown) {
      throw AuthService.handleError(error, '获取用户信息失败');
    }
  }

  /**
   * 更新用户资料
   * @param data 更新数据
   * @returns 更新后的用户信息
   */
  static async updateProfile(data: Partial<AuthUser>): Promise<AuthUser> {
    try {
      const response = await apiClient.put('/users/profile', data);
      return response.data;
    } catch (error: unknown) {
      throw AuthService.handleError(error, '更新用户资料失败');
    }
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      await apiClient.post('/users/logout');
    } catch (error: unknown) {
      // 登出失败不抛出错误，允许本地清理
      logger.warn('服务端登出失败:', error);
    }
  }

  /**
   * 处理API错误
   * @param error 原始错误
   * @param defaultMessage 默认错误消息
   * @returns 标准化的API错误
   */
  private static handleError(error: unknown, defaultMessage: string): ApiError {
    // 类型检查：确保error是一个对象
    if (error && typeof error === 'object' && 'response' in error) {
      // 服务器响应错误
      const axiosError = error as {
        response: { status: number; data?: Record<string, unknown> };
      };
      const { status, data } = axiosError.response;
      const message =
        (data?.detail as string) || (data?.message as string) || defaultMessage;
      return new ApiError(message, status, data?.code as string);
    } else if (error && typeof error === 'object' && 'request' in error) {
      // 网络错误
      return new ApiError('网络连接失败，请检查网络状态', 0);
    } else {
      // 其他错误
      const message =
        error && typeof error === 'object' && 'message' in error
          ? (error as { message: string }).message
          : defaultMessage;
      return new ApiError(message, 0);
    }
  }
}

// 导出实例方法以便直接使用
export const authService = {
  sendSmsCode: AuthService.sendSmsCode.bind(AuthService),
  phoneLogin: AuthService.phoneLogin.bind(AuthService),
  refreshToken: AuthService.refreshToken.bind(AuthService),
  getCurrentUser: AuthService.getCurrentUser.bind(AuthService),
  updateProfile: AuthService.updateProfile.bind(AuthService),
  logout: AuthService.logout.bind(AuthService),
};

export default authService;
