/**
 * 手机号认证表单组件
 */
import React, { useState, useCallback } from 'react';
import { Form, Input, Button, Typography, message } from 'antd';
import { MobileOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import type { ApiError, PhoneAuthFormProps } from '@/types';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { sendSmsCode, phoneLogin, clearError } from '@store/slices/authSlice';
import { useCountdown } from '@hooks/useCountdown';
import SmsCodeInput from '@components/common/SmsCodeInput';
import { validatePhone } from '@utils/phoneValidator';
import { AUTH } from '@constants/index';
import { colors, spacing } from '@styles/theme';

const { Link } = Typography;

const Container = styled.div`
  width: 100%;
`;

const PhoneInputWrapper = styled.div`
  display: flex;
  align-items: stretch;
  gap: 0;

  .ant-input-affix-wrapper {
    flex: 1;
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-top-left-radius: 8px !important;
    border-bottom-left-radius: 8px !important;

    &:hover,
    &:focus-within {
      border-right: none !important;
      z-index: 1;
    }
  }

  .verify-code-btn {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-top-right-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
    border-left: none !important;
    min-width: 100px;
    height: auto;
    font-size: 14px;

    &:hover,
    &:focus {
      border-left: none !important;
      z-index: 1;
    }
  }
`;

const SmsSection = styled.div`
  margin-bottom: ${spacing.lg};
  text-align: center;
`;

const Agreement = styled.div`
  text-align: center;
  color: ${colors.text.tertiary};
  font-size: 14px;
  line-height: 20px;
  margin-top: ${spacing.md};

  a {
    color: ${colors.primary};
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
`;

/**
 * 手机号认证表单组件
 */
const PhoneAuthForm: React.FC<PhoneAuthFormProps> = ({
  onSuccess,
  onError,
}) => {
  const dispatch = useAppDispatch();
  const { loading, error } = useAppSelector(state => state.auth);

  const [phone, setPhone] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [form] = Form.useForm();

  const {
    seconds,
    isActive: isCountdownActive,
    start: startCountdown,
  } = useCountdown({
    initialSeconds: AUTH.SMS_RESEND_COUNTDOWN,
  });

  /**
   * 处理手机号输入
   */
  const handlePhoneChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value.replace(/\D/g, ''); // 只保留数字
      if (value.length <= 11) {
        setPhone(value);
        // 清除之前的错误
        if (error) {
          dispatch(clearError());
        }
      }
    },
    [error, dispatch]
  );

  /**
   * 发送短信验证码
   */
  const handleSendSms = useCallback(async () => {
    const validation = validatePhone(phone);

    if (!validation.isValid) {
      message.error('请输入正确的手机号格式');
      return;
    }

    try {
      await dispatch(sendSmsCode({ phone })).unwrap();
      message.success('验证码已发送到您的手机');
      setIsCodeSent(true);
      startCountdown();
    } catch (err: unknown) {
      const error = err as ApiError;
      message.error(error.message || '发送验证码失败');
      onError?.(error);
    }
  }, [phone, dispatch, startCountdown, onError]);

  /**
   * 处理验证码输入完成
   */
  const handleSmsCodeComplete = useCallback((code: string) => {
    setSmsCode(code);
  }, []);

  /**
   * 提交登录
   */
  const handleLogin = useCallback(async () => {
    const validation = validatePhone(phone);

    if (!validation.isValid) {
      message.error('请输入正确的手机号格式');
      return;
    }

    if (smsCode.length !== 6) {
      message.error('请输入完整的验证码');
      return;
    }

    try {
      const response = await dispatch(
        phoneLogin({
          phone,
          verification_code: smsCode,
        })
      ).unwrap();

      message.success('登录成功');
      onSuccess?.(response);
    } catch (err: unknown) {
      const error = err as ApiError;
      message.error(error.message || '登录失败');
      setSmsCode(''); // 清空验证码
      onError?.(error);
    }
  }, [phone, smsCode, dispatch, onSuccess, onError]);

  // 获取手机号验证状态
  const phoneValidation = validatePhone(phone);

  // 是否可以登录
  const canLogin = phoneValidation.isValid && smsCode.length === 6;

  return (
    <Container>
      <Form form={form} layout='vertical' size='large'>
        {/* 手机号输入框和验证码按钮 */}
        <Form.Item
          validateStatus={phone && !phoneValidation.isValid ? 'error' : ''}
          help={
            phone && !phoneValidation.isValid ? (
              '请输入正确的手机号格式'
            ) : (
              <div style={{ height: '24px' }}></div>
            )
          }
          style={{ marginBottom: 0 }}
        >
          <PhoneInputWrapper>
            <Input
              prefix={
                <MobileOutlined style={{ color: colors.text.tertiary }} />
              }
              placeholder='请输入手机号'
              value={phone}
              onChange={handlePhoneChange}
              maxLength={11}
              autoComplete='tel'
              size='large'
            />
            <Button
              className='verify-code-btn'
              loading={loading && !isCodeSent}
              onClick={handleSendSms}
              disabled={!phoneValidation.isValid || isCountdownActive}
              size='large'
            >
              {isCountdownActive ? `${seconds}s后重发` : '验证码'}
            </Button>
          </PhoneInputWrapper>
        </Form.Item>

        {/* 验证码输入区域 - 直接显示在手机号下方 */}
        <SmsSection>
          <SmsCodeInput
            value={smsCode}
            onChange={setSmsCode}
            onComplete={handleSmsCodeComplete}
            disabled={loading || !isCodeSent}
          />
        </SmsSection>

        {/* 登录按钮 */}
        <Form.Item style={{ marginBottom: 0 }}>
          <Button
            type='primary'
            loading={loading && isCodeSent}
            onClick={handleLogin}
            disabled={!canLogin}
            block
            size='large'
          >
            {loading && isCodeSent ? '登录中...' : '登录'}
          </Button>
        </Form.Item>
      </Form>

      {/* 用户协议 */}
      <Agreement>
        登录表示同意{' '}
        <Link href='/agreement' target='_blank'>
          用户协议
        </Link>{' '}
        和{' '}
        <Link href='/privacy' target='_blank'>
          隐私政策
        </Link>
      </Agreement>
    </Container>
  );
};

export default PhoneAuthForm;
