/**
 * 加载中组件
 */
import React from 'react';
import { Spin } from 'antd';
import { CommonProps } from '@/types';

interface LoadingProps extends CommonProps {
  /** 加载提示文字 */
  tip?: string;
  /** 尺寸 */
  size?: 'small' | 'default' | 'large';
  /** 是否显示 */
  spinning?: boolean;
}

/**
 * 加载中组件
 */
const Loading: React.FC<LoadingProps> = ({
  tip = '加载中...',
  size = 'default',
  spinning = true,
  className,
  style,
  children,
}) => {
  const wrapperStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: children ? 'auto' : '200px',
    ...style,
  };

  return (
    <div className={className} style={wrapperStyle}>
      <Spin tip={tip} size={size} spinning={spinning}>
        {children}
      </Spin>
    </div>
  );
};

export default Loading;
