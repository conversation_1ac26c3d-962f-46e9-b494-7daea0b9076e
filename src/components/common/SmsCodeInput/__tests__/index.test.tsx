/**
 * SmsCodeInput 组件测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from 'styled-components';

import SmsCodeInput from '../index';
import { theme } from '@styles/theme';

// Mock theme provider
const renderWithTheme = (component: React.ReactElement) => {
  return render(<ThemeProvider theme={theme}>{component}</ThemeProvider>);
};

describe('SmsCodeInput', () => {
  const mockOnChange = jest.fn();
  const mockOnComplete = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
    mockOnComplete.mockClear();
  });

  it('应该渲染6个输入框', () => {
    renderWithTheme(<SmsCodeInput value='' onChange={mockOnChange} />);

    const inputs = screen.getAllByRole('textbox');
    expect(inputs).toHaveLength(6);
  });

  it('应该在输入数字时调用onChange', async () => {
    const user = userEvent.setup();
    renderWithTheme(<SmsCodeInput value='' onChange={mockOnChange} />);

    const firstInput = screen.getAllByRole('textbox')[0];
    await user.type(firstInput, '1');

    expect(mockOnChange).toHaveBeenCalledWith('1');
  });

  it('应该在输入完成时自动移动到下一个输入框', async () => {
    const user = userEvent.setup();
    renderWithTheme(<SmsCodeInput value='' onChange={mockOnChange} />);

    const inputs = screen.getAllByRole('textbox');
    await user.type(inputs[0], '1');

    // 第二个输入框应该获得焦点
    await waitFor(() => {
      expect(inputs[1]).toHaveFocus();
    });
  });

  it('应该在输入6位数字时调用onComplete', async () => {
    const user = userEvent.setup();
    renderWithTheme(
      <SmsCodeInput
        value=''
        onChange={mockOnChange}
        onComplete={mockOnComplete}
      />
    );

    const inputs = screen.getAllByRole('textbox');

    // 依次输入6位数字
    for (let i = 0; i < 6; i++) {
      await user.type(inputs[i], String(i + 1));
    }

    expect(mockOnComplete).toHaveBeenCalledWith('123456');
  });

  it('应该只允许输入数字', async () => {
    const user = userEvent.setup();
    renderWithTheme(<SmsCodeInput value='' onChange={mockOnChange} />);

    const firstInput = screen.getAllByRole('textbox')[0];
    await user.type(firstInput, 'a');

    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it('应该处理退格键', async () => {
    const user = userEvent.setup();
    renderWithTheme(<SmsCodeInput value='123' onChange={mockOnChange} />);

    const inputs = screen.getAllByRole('textbox');

    // 焦点移到第三个输入框（有值）
    inputs[2].focus();
    await user.keyboard('{Backspace}');

    expect(mockOnChange).toHaveBeenCalledWith('12');
  });

  it('应该处理左右箭头键导航', async () => {
    const user = userEvent.setup();
    renderWithTheme(<SmsCodeInput value='123' onChange={mockOnChange} />);

    const inputs = screen.getAllByRole('textbox');

    // 焦点在第二个输入框
    inputs[1].focus();

    // 按左箭头键
    await user.keyboard('{ArrowLeft}');
    expect(inputs[0]).toHaveFocus();

    // 按右箭头键
    await user.keyboard('{ArrowRight}');
    expect(inputs[1]).toHaveFocus();
  });

  it('应该处理粘贴事件', async () => {
    renderWithTheme(
      <SmsCodeInput
        value=''
        onChange={mockOnChange}
        onComplete={mockOnComplete}
      />
    );

    const firstInput = screen.getAllByRole('textbox')[0];

    // 模拟粘贴6位数字
    await userEvent.click(firstInput);

    const clipboardData = {
      getData: () => '123456',
    };

    fireEvent.paste(firstInput, {
      clipboardData,
    });

    expect(mockOnChange).toHaveBeenCalledWith('123456');
    expect(mockOnComplete).toHaveBeenCalledWith('123456');
  });

  it('应该处理粘贴包含非数字字符的内容', async () => {
    renderWithTheme(<SmsCodeInput value='' onChange={mockOnChange} />);

    const firstInput = screen.getAllByRole('textbox')[0];

    const clipboardData = {
      getData: () => '1a2b3c4d5e6f',
    };

    fireEvent.paste(firstInput, {
      clipboardData,
    });

    expect(mockOnChange).toHaveBeenCalledWith('123456');
  });

  it('应该正确同步外部value', () => {
    const { rerender } = renderWithTheme(
      <SmsCodeInput value='123' onChange={mockOnChange} />
    );

    const inputs = screen.getAllByRole('textbox');
    expect(inputs[0]).toHaveValue('1');
    expect(inputs[1]).toHaveValue('2');
    expect(inputs[2]).toHaveValue('3');
    expect(inputs[3]).toHaveValue('');

    // 更新外部value
    rerender(
      <ThemeProvider theme={theme}>
        <SmsCodeInput value='456789' onChange={mockOnChange} />
      </ThemeProvider>
    );

    expect(inputs[0]).toHaveValue('4');
    expect(inputs[1]).toHaveValue('5');
    expect(inputs[2]).toHaveValue('6');
    expect(inputs[3]).toHaveValue('7');
    expect(inputs[4]).toHaveValue('8');
    expect(inputs[5]).toHaveValue('9');
  });

  it('应该在禁用状态下不响应输入', async () => {
    const user = userEvent.setup();
    renderWithTheme(
      <SmsCodeInput value='' onChange={mockOnChange} disabled={true} />
    );

    const inputs = screen.getAllByRole('textbox');

    // 所有输入框应该被禁用
    inputs.forEach(input => {
      expect(input).toBeDisabled();
    });

    // 尝试输入应该无效
    await user.type(inputs[0], '1');
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it('应该在聚焦时选中内容', async () => {
    const user = userEvent.setup();
    renderWithTheme(<SmsCodeInput value='123456' onChange={mockOnChange} />);

    const firstInput = screen.getAllByRole('textbox')[0];
    await user.click(firstInput);

    // 验证input的选中状态（这在实际DOM中会发生）
    expect(firstInput).toHaveFocus();
  });
});
