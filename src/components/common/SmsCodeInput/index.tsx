/**
 * 短信验证码输入组件
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';

import type { SmsCodeInputProps } from '@/types';
import { VALIDATION } from '@constants/index';
import { colors, sizes, borderRadius, shadows, animation } from '@styles/theme';

const Container = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
`;

const InputBox = styled.input<{ $hasValue: boolean; $isDisabled: boolean }>`
  width: 48px;
  height: ${sizes.input.large};
  background-color: ${colors.background.secondary};
  border: 2px solid
    ${props => (props.$hasValue ? colors.primary : colors.border.default)};
  border-radius: ${borderRadius.medium};
  color: ${colors.text.primary};
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  outline: none;
  transition: all ${animation.duration.normal} ${animation.easing.standard};
  cursor: ${props => (props.$isDisabled ? 'not-allowed' : 'text')};
  opacity: ${props => (props.$isDisabled ? colors.disabled.opacity : 1)};

  &:hover:not(:disabled) {
    border-color: ${colors.border.focus};
  }

  &:focus {
    border-color: ${colors.primary};
    box-shadow: ${shadows.focus};
  }

  &::placeholder {
    color: ${colors.text.placeholder};
    font-size: 16px;
  }
`;

/**
 * 短信验证码输入组件
 */
const SmsCodeInput: React.FC<SmsCodeInputProps> = ({
  value = '',
  onChange,
  onComplete,
  disabled = false,
  autoFocus = true,
}) => {
  const [codes, setCodes] = useState<string[]>(['', '', '', '', '', '']);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // 同步外部value到内部状态
  useEffect(() => {
    if (value && value.length <= VALIDATION.SMS_CODE_LENGTH) {
      const newCodes = value
        .split('')
        .concat(Array(VALIDATION.SMS_CODE_LENGTH - value.length).fill(''));
      setCodes(newCodes.slice(0, VALIDATION.SMS_CODE_LENGTH));
    }
  }, [value]);

  // 自动聚焦第一个输入框
  useEffect(() => {
    if (autoFocus && !disabled && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus, disabled]);

  /**
   * 处理输入变化
   */
  const handleInputChange = useCallback(
    (index: number, inputValue: string) => {
      // 只允许输入数字
      const digit = inputValue.replace(/\D/g, '');
      if (digit.length > 1) return;

      // 如果输入的不是数字，不做任何处理
      if (inputValue && !digit) return;

      const newCodes = [...codes];
      newCodes[index] = digit;
      setCodes(newCodes);

      const newValue = newCodes.join('');
      onChange?.(newValue);

      // 自动移动到下一个输入框
      if (digit && index < VALIDATION.SMS_CODE_LENGTH - 1) {
        inputRefs.current[index + 1]?.focus();
      }

      // 检查是否完成输入
      if (newValue.length === VALIDATION.SMS_CODE_LENGTH) {
        onComplete?.(newValue);
      }
    },
    [codes, onChange, onComplete]
  );

  /**
   * 处理键盘事件
   */
  const handleKeyDown = useCallback(
    (index: number, event: React.KeyboardEvent<HTMLInputElement>) => {
      const { key } = event;

      // 处理退格键
      if (key === 'Backspace') {
        event.preventDefault();
        const newCodes = [...codes];

        if (codes[index]) {
          // 当前位置有值，清除当前位置
          newCodes[index] = '';
        } else if (index > 0) {
          // 当前位置无值，移动到上一个位置并清除
          newCodes[index - 1] = '';
          inputRefs.current[index - 1]?.focus();
        }

        setCodes(newCodes);
        onChange?.(newCodes.join(''));
      }

      // 处理左右箭头键
      if (key === 'ArrowLeft' && index > 0) {
        inputRefs.current[index - 1]?.focus();
      }
      if (key === 'ArrowRight' && index < VALIDATION.SMS_CODE_LENGTH - 1) {
        inputRefs.current[index + 1]?.focus();
      }

      // 处理数字键
      if (/^\d$/.test(key)) {
        // 如果当前位置有值，先清除
        if (codes[index]) {
          const newCodes = [...codes];
          newCodes[index] = '';
          setCodes(newCodes);
        }
      }
    },
    [codes, onChange]
  );

  /**
   * 处理粘贴事件
   */
  const handlePaste = useCallback(
    (event: React.ClipboardEvent<HTMLInputElement>) => {
      event.preventDefault();
      const pastedText = (
        event.clipboardData ||
        (window as unknown as { clipboardData: unknown }).clipboardData
      ).getData('text');
      const digits = pastedText
        .replace(/\D/g, '')
        .slice(0, VALIDATION.SMS_CODE_LENGTH);

      if (digits.length > 0) {
        const newCodes = digits
          .split('')
          .concat(Array(VALIDATION.SMS_CODE_LENGTH - digits.length).fill(''));
        setCodes(newCodes.slice(0, VALIDATION.SMS_CODE_LENGTH));
        onChange?.(digits);

        // 聚焦到最后一个输入的位置
        const nextIndex = Math.min(
          digits.length,
          VALIDATION.SMS_CODE_LENGTH - 1
        );
        inputRefs.current[nextIndex]?.focus();

        if (digits.length === VALIDATION.SMS_CODE_LENGTH) {
          onComplete?.(digits);
        }
      }
    },
    [onChange, onComplete]
  );

  /**
   * 处理聚焦事件
   */
  const handleFocus = useCallback(
    (event: React.FocusEvent<HTMLInputElement>) => {
      // 聚焦时选中全部内容
      event.target.select();
    },
    []
  );

  return (
    <Container>
      {codes.map((code, index) => (
        <InputBox
          key={index}
          ref={el => (inputRefs.current[index] = el)}
          type='text'
          value={code}
          onChange={e => handleInputChange(index, e.target.value)}
          onKeyDown={e => handleKeyDown(index, e)}
          onPaste={handlePaste}
          onFocus={handleFocus}
          disabled={disabled}
          maxLength={1}
          $hasValue={!!code}
          $isDisabled={disabled}
          placeholder='·'
          autoComplete='one-time-code'
          inputMode='numeric'
        />
      ))}
    </Container>
  );
};

export default SmsCodeInput;
