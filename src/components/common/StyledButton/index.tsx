/**
 * 样式化按钮组件
 * 基于UI设计规范的自定义按钮
 */
import React from 'react';
import { Button, ButtonProps } from 'antd';
import { colors, sizes, borderRadius, animation } from '@styles/theme';

/**
 * 样式化按钮属性接口
 */
interface StyledButtonProps extends Omit<ButtonProps, 'variant' | 'size'> {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  size?: 'small' | 'medium' | 'large';
}

/**
 * 获取按钮变体样式
 */
const getVariantStyles = (variant: string) => {
  const baseStyles = {
    border: 'none',
    borderRadius: borderRadius.medium,
    fontWeight: 500,
    transition: `all ${animation.duration.normal} ${animation.easing.standard}`,
    cursor: 'pointer',
  };

  switch (variant) {
    case 'primary':
      return {
        ...baseStyles,
        background: colors.primary,
        color: colors.text.primary,
        boxShadow: '0 2px 8px rgba(79, 70, 229, 0.3)',
      };
    case 'secondary':
      return {
        ...baseStyles,
        background: colors.background.secondary,
        color: colors.text.primary,
        border: `1px solid ${colors.border.default}`,
      };
    case 'success':
      return {
        ...baseStyles,
        background: colors.success,
        color: colors.text.primary,
        boxShadow: '0 2px 8px rgba(16, 185, 129, 0.3)',
      };
    case 'warning':
      return {
        ...baseStyles,
        background: colors.warning,
        color: colors.text.primary,
        boxShadow: '0 2px 8px rgba(245, 158, 11, 0.3)',
      };
    case 'error':
      return {
        ...baseStyles,
        background: colors.error,
        color: colors.text.primary,
        boxShadow: '0 2px 8px rgba(239, 68, 68, 0.3)',
      };
    default:
      return baseStyles;
  }
};

/**
 * 获取按钮尺寸样式
 */
const getSizeStyles = (size: string) => {
  switch (size) {
    case 'small':
      return {
        height: sizes.button.small,
        padding: '0 16px',
        fontSize: '14px',
      };
    case 'large':
      return {
        height: sizes.button.large,
        padding: '0 32px',
        fontSize: '16px',
      };
    case 'medium':
    default:
      return {
        height: sizes.button.medium,
        padding: '0 24px',
        fontSize: '16px',
      };
  }
};

/**
 * 样式化按钮组件
 */
const StyledButton: React.FC<StyledButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  style,
  children,
  ...props
}) => {
  const variantStyles = getVariantStyles(variant);
  const sizeStyles = getSizeStyles(size);

  const combinedStyles = {
    ...variantStyles,
    ...sizeStyles,
    ...style,
  };

  return (
    <Button {...props} style={combinedStyles}>
      {children}
    </Button>
  );
};

export default StyledButton;
export type { StyledButtonProps };
