import React, { useState } from 'react';
import { Card, Descriptions, Tag, Button, Space, Collapse, Alert } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import {
  useConfig,
  useEnvironment,
  useFeatureFlags,
  useApiConfig,
  useThemeConfig,
} from '@/config';

const { Panel } = Collapse;

/**
 * 配置演示组件
 * 展示如何在组件中使用环境变量配置
 */
const ConfigDemo: React.FC = () => {
  const [showSensitive, setShowSensitive] = useState(false);

  // 使用配置相关的 Hooks
  const { config, environment, isDevelopment } = useConfig();
  const { isDev, isProd, isTest } = useEnvironment();
  const { isMockDataEnabled, isDebugModeEnabled, isHotReloadEnabled } =
    useFeatureFlags();
  const { apiConfig, buildApiUrl, buildVersionedApiUrl } = useApiConfig();
  const { themeConfig, primaryColor, successColor } = useThemeConfig();

  /**
   * 获取环境标签颜色
   */
  const getEnvironmentColor = () => {
    switch (environment) {
      case 'development':
        return 'blue';
      case 'production':
        return 'green';
      case 'test':
        return 'orange';
      default:
        return 'default';
    }
  };

  /**
   * 渲染敏感信息
   */
  const renderSensitiveInfo = (value: string) => {
    if (!showSensitive) {
      return '••••••••••••';
    }
    return value;
  };

  return (
    <div style={{ padding: '20px' }}>
      <Space direction='vertical' size='large' style={{ width: '100%' }}>
        <Alert
          message='配置演示组件'
          description='此组件展示了如何在 React 组件中使用环境变量配置。仅在开发环境下显示。'
          type='info'
          showIcon
        />

        <Card title='基础环境信息' size='small'>
          <Descriptions column={2} size='small'>
            <Descriptions.Item label='应用名称'>
              {config.name}
            </Descriptions.Item>
            <Descriptions.Item label='版本'>{config.version}</Descriptions.Item>
            <Descriptions.Item label='当前环境'>
              <Tag color={getEnvironmentColor()}>{environment}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label='环境检查'>
              <Space>
                <Tag color={isDev() ? 'blue' : 'default'}>
                  开发环境: {isDev() ? '是' : '否'}
                </Tag>
                <Tag color={isProd() ? 'green' : 'default'}>
                  生产环境: {isProd() ? '是' : '否'}
                </Tag>
                <Tag color={isTest() ? 'orange' : 'default'}>
                  测试环境: {isTest() ? '是' : '否'}
                </Tag>
              </Space>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Collapse>
          <Panel header='API 配置' key='api'>
            <Card size='small'>
              <Descriptions column={1} size='small'>
                <Descriptions.Item label='API 基础 URL'>
                  {apiConfig.baseUrl}
                </Descriptions.Item>
                <Descriptions.Item label='API 版本'>
                  {apiConfig.version}
                </Descriptions.Item>
                <Descriptions.Item label='请求超时'>
                  {apiConfig.timeout}ms
                </Descriptions.Item>
                <Descriptions.Item label='示例 URL'>
                  {buildApiUrl('/users')}
                </Descriptions.Item>
                <Descriptions.Item label='版本化 URL'>
                  {buildVersionedApiUrl('/users')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Panel>

          <Panel header='功能开关' key='features'>
            <Card size='small'>
              <Space wrap>
                <Tag color={isMockDataEnabled ? 'green' : 'red'}>
                  Mock 数据: {isMockDataEnabled ? '启用' : '禁用'}
                </Tag>
                <Tag color={isDebugModeEnabled ? 'green' : 'red'}>
                  调试模式: {isDebugModeEnabled ? '启用' : '禁用'}
                </Tag>
                <Tag color={isHotReloadEnabled ? 'green' : 'red'}>
                  热重载: {isHotReloadEnabled ? '启用' : '禁用'}
                </Tag>
              </Space>
            </Card>
          </Panel>

          <Panel header='主题配置' key='theme'>
            <Card size='small'>
              <Descriptions column={2} size='small'>
                <Descriptions.Item label='默认主题'>
                  {themeConfig.defaultTheme}
                </Descriptions.Item>
                <Descriptions.Item label='主色调'>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <div
                      style={{
                        width: '20px',
                        height: '20px',
                        backgroundColor: primaryColor,
                        borderRadius: '4px',
                        border: '1px solid #d9d9d9',
                      }}
                    />
                    {primaryColor}
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label='成功色'>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <div
                      style={{
                        width: '20px',
                        height: '20px',
                        backgroundColor: successColor,
                        borderRadius: '4px',
                        border: '1px solid #d9d9d9',
                      }}
                    />
                    {successColor}
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label='警告色'>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <div
                      style={{
                        width: '20px',
                        height: '20px',
                        backgroundColor: themeConfig.warningColor,
                        borderRadius: '4px',
                        border: '1px solid #d9d9d9',
                      }}
                    />
                    {themeConfig.warningColor}
                  </div>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Panel>

          <Panel header='认证配置' key='auth'>
            <Card
              size='small'
              extra={
                <Button
                  type='text'
                  size='small'
                  icon={
                    showSensitive ? <EyeInvisibleOutlined /> : <EyeOutlined />
                  }
                  onClick={() => setShowSensitive(!showSensitive)}
                >
                  {showSensitive ? '隐藏' : '显示'}敏感信息
                </Button>
              }
            >
              <Descriptions column={1} size='small'>
                <Descriptions.Item label='JWT 密钥'>
                  {renderSensitiveInfo(config.auth.jwtSecretKey)}
                </Descriptions.Item>
                <Descriptions.Item label='JWT 过期时间'>
                  {config.auth.jwtExpiresIn}
                </Descriptions.Item>
                <Descriptions.Item label='刷新令牌过期时间'>
                  {config.auth.refreshTokenExpiresIn}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Panel>

          <Panel header='分页配置' key='pagination'>
            <Card size='small'>
              <Descriptions column={2} size='small'>
                <Descriptions.Item label='默认页面大小'>
                  {config.pagination.defaultPageSize}
                </Descriptions.Item>
                <Descriptions.Item label='最大页面大小'>
                  {config.pagination.maxPageSize}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Panel>
        </Collapse>

        {isDevelopment && (
          <Alert
            message='开发环境提示'
            description='当前为开发环境，可以在控制台查看详细的配置信息。在生产环境中，敏感信息将被隐藏。'
            type='warning'
            showIcon
          />
        )}
      </Space>
    </div>
  );
};

export default ConfigDemo;
