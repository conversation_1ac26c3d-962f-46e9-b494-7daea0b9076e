/**
 * 样式化卡片组件
 * 基于UI设计规范的自定义卡片
 */
import React from 'react';
import { Card, CardProps } from 'antd';
import { colors, borderRadius, shadows, spacing } from '@styles/theme';

/**
 * 样式化卡片属性接口
 */
interface StyledCardProps extends Omit<CardProps, 'variant'> {
  variant?: 'default' | 'elevated' | 'outlined' | 'flat';
  hover?: boolean;
}

/**
 * 获取卡片变体样式
 */
const getVariantStyles = (variant: string, hover: boolean) => {
  const baseStyles = {
    background: colors.background.card,
    borderRadius: borderRadius.large,
    border: `1px solid ${colors.border.default}`,
    transition: 'all 0.3s ease',
  };

  switch (variant) {
    case 'elevated':
      return {
        ...baseStyles,
        boxShadow: shadows.card,
        border: 'none',
        ...(hover && {
          ':hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.5)',
          },
        }),
      };
    case 'outlined':
      return {
        ...baseStyles,
        border: `2px solid ${colors.border.default}`,
        boxShadow: 'none',
        ...(hover && {
          ':hover': {
            borderColor: colors.primary,
            boxShadow: shadows.focus,
          },
        }),
      };
    case 'flat':
      return {
        ...baseStyles,
        border: 'none',
        boxShadow: 'none',
        background: colors.background.secondary,
      };
    case 'default':
    default:
      return {
        ...baseStyles,
        boxShadow: shadows.card,
        ...(hover && {
          ':hover': {
            borderColor: colors.border.hover,
          },
        }),
      };
  }
};

/**
 * 样式化卡片组件
 */
const StyledCard: React.FC<StyledCardProps> = ({
  variant = 'default',
  hover = false,
  style,
  bodyStyle,
  children,
  ...props
}) => {
  const variantStyles = getVariantStyles(variant, hover);

  const combinedStyles = {
    ...variantStyles,
    ...style,
  };

  const combinedBodyStyles = {
    padding: spacing.lg,
    ...bodyStyle,
  };

  return (
    <Card
      {...props}
      style={combinedStyles}
      bodyStyle={combinedBodyStyles}
      bordered={false}
    >
      {children}
    </Card>
  );
};

export default StyledCard;
export type { StyledCardProps };
