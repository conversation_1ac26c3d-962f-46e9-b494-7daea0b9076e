/**
 * 应用常量定义
 */

// API 相关常量
export const API_BASE_URL =
  process.env.REACT_APP_API_BASE_URL +
    '/' +
    process.env.REACT_APP_API_VERSION || 'http://localhost:8000';
export const API_TIMEOUT = 10000;

// 存储键名常量
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'finsight_access_token',
  REFRESH_TOKEN: 'finsight_refresh_token',
  USER_INFO: 'finsight_user_info',
  TOKEN_EXPIRES_AT: 'finsight_token_expires_at',
  THEME: 'theme_preference',
  LANGUAGE: 'language_preference',
} as const;

// 路由路径常量
export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  NEWS_FLASH: '/news-flash',
  CALENDAR: '/calendar',
  HEADLINES: '/headlines',
  TOPICS: '/topics',
  REPORTS: '/reports',
  SETTINGS: '/settings',
  LOGIN: '/login',
} as const;

// 主题常量
export const THEME_COLORS = {
  PRIMARY: '#1890ff',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#ff4d4f',
  INFO: '#1890ff',
} as const;

// 分页常量
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
} as const;

// 表单验证常量
export const VALIDATION = {
  MIN_PASSWORD_LENGTH: 6,
  MAX_PASSWORD_LENGTH: 20,
  EMAIL_PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_PATTERN: /^1[3-9]\d{9}$/,
  SMS_CODE_LENGTH: 6,
  SMS_CODE_PATTERN: /^\d{6}$/,
} as const;

// 文件上传常量
export const UPLOAD = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword'],
} as const;

// 默认配置
export const DEFAULT_CONFIG = {
  LANGUAGE: 'zh-CN',
  THEME: 'light',
  CURRENCY: 'CNY',
  TIMEZONE: 'Asia/Shanghai',
} as const;

// 认证相关常量
export const AUTH = {
  SMS_CODE_EXPIRES: 300, // 验证码有效期5分钟
  SMS_RESEND_COUNTDOWN: 60, // 重发倒计时60秒
  TOKEN_REFRESH_THRESHOLD: 300, // token刷新阈值5分钟
  MAX_LOGIN_ATTEMPTS: 3, // 最大登录尝试次数
} as const;

// 手机号运营商映射
export const PHONE_CARRIERS = {
  '134': '中国移动',
  '135': '中国移动',
  '136': '中国移动',
  '137': '中国移动',
  '138': '中国移动',
  '139': '中国移动',
  '147': '中国移动',
  '150': '中国移动',
  '151': '中国移动',
  '152': '中国移动',
  '157': '中国移动',
  '158': '中国移动',
  '159': '中国移动',
  '178': '中国移动',
  '182': '中国移动',
  '183': '中国移动',
  '184': '中国移动',
  '187': '中国移动',
  '188': '中国移动',
  '198': '中国移动',
  '130': '中国联通',
  '131': '中国联通',
  '132': '中国联通',
  '145': '中国联通',
  '155': '中国联通',
  '156': '中国联通',
  '166': '中国联通',
  '175': '中国联通',
  '176': '中国联通',
  '185': '中国联通',
  '186': '中国联通',
  '133': '中国电信',
  '149': '中国电信',
  '153': '中国电信',
  '173': '中国电信',
  '177': '中国电信',
  '180': '中国电信',
  '181': '中国电信',
  '189': '中国电信',
  '199': '中国电信',
} as const;
