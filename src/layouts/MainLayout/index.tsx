/**
 * 主布局组件
 */
import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout } from 'antd';

import Sidebar from './Sidebar';
import Header from './Header';
import { colors, spacing, borderRadius } from '@styles/theme';

const { Content } = Layout;

/**
 * 主布局组件
 */
const MainLayout: React.FC = () => {
  return (
    <Layout
      style={{
        minHeight: '100vh',
        background: colors.background.primary,
      }}
    >
      <Sidebar />
      <Layout style={{ background: colors.background.primary }}>
        <Header />
        <Content
          style={{
            margin: spacing.lg,
            padding: spacing.lg,
            background: colors.background.card,
            borderRadius: borderRadius.large,
            border: `1px solid ${colors.border.default}`,
            minHeight: 'calc(100vh - 160px)', // 减去header和margin的高度
            overflow: 'auto',
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
