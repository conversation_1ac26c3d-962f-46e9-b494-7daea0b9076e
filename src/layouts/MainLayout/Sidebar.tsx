/**
 * 侧边栏组件
 */
import React from 'react';
import { Layout, Menu } from 'antd';
import {
  DashboardOutlined,
  BarChartOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

import { ROUTES } from '@constants/index';
import { colors, typography, spacing, shadows } from '@styles/theme';

const { Sider } = Layout;

/**
 * 菜单项配置
 */
const menuItems = [
  {
    key: ROUTES.DASHBOARD,
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  {
    key: ROUTES.REPORTS,
    icon: <BarChartOutlined />,
    label: '报表分析',
  },
  {
    key: ROUTES.SETTINGS,
    icon: <SettingOutlined />,
    label: '系统设置',
  },
];

/**
 * 侧边栏组件
 */
const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Sider
      width={240}
      style={{
        background: colors.background.card,
        boxShadow: shadows.card,
        borderRight: `1px solid ${colors.border.default}`,
      }}
    >
      {/* Logo区域 */}
      <div
        style={{
          height: 80,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: `1px solid ${colors.border.default}`,
          fontSize: typography.h2.fontSize,
          fontWeight: typography.h2.fontWeight,
          color: colors.primary,
          padding: `0 ${spacing.lg}`,
          background: colors.background.card,
        }}
      >
        <div
          style={{
            background: `linear-gradient(45deg, ${colors.primary}, ${colors.info})`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            fontWeight: 700,
          }}
        >
          FinSight
        </div>
      </div>

      {/* 菜单区域 */}
      <div style={{ padding: `${spacing.md} 0` }}>
        <Menu
          mode='inline'
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{
            borderRight: 0,
            background: 'transparent',
          }}
        />
      </div>
    </Sider>
  );
};

export default Sidebar;
