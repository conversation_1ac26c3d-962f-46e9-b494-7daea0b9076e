/**
 * 头部组件
 */
import React from 'react';
import { Layout, Avatar, Dropdown, Space, Badge, Button } from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';

import { colors, typography, spacing } from '@styles/theme';

const { Header: AntHeader } = Layout;

/**
 * 用户下拉菜单项
 */
const userMenuItems = [
  {
    key: 'profile',
    icon: <UserOutlined />,
    label: '个人资料',
  },
  {
    key: 'settings',
    icon: <SettingOutlined />,
    label: '账户设置',
  },
  {
    type: 'divider' as const,
  },
  {
    key: 'logout',
    icon: <LogoutOutlined />,
    label: '退出登录',
    danger: true,
  },
];

/**
 * 头部组件
 */
const Header: React.FC = () => {
  const [collapsed, setCollapsed] = React.useState(false);

  const handleMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        break;
      case 'settings':
        break;
      case 'logout':
        break;
      default:
        break;
    }
  };

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  return (
    <AntHeader
      style={{
        background: colors.background.card,
        padding: `0 ${spacing.lg}`,
        borderBottom: `1px solid ${colors.border.default}`,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: 80,
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      }}
    >
      {/* 左侧菜单切换按钮 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Button
          type='text'
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={toggleCollapsed}
          style={{
            fontSize: '18px',
            width: 48,
            height: 48,
            color: colors.text.secondary,
          }}
        />
      </div>

      {/* 右侧用户操作区 */}
      <div style={{ display: 'flex', alignItems: 'center', gap: spacing.md }}>
        {/* 通知铃铛 */}
        <Badge count={3} size='small'>
          <Button
            type='text'
            icon={<BellOutlined />}
            style={{
              fontSize: '18px',
              width: 48,
              height: 48,
              color: colors.text.secondary,
            }}
          />
        </Badge>

        {/* 用户信息下拉菜单 */}
        <Dropdown
          menu={{
            items: userMenuItems,
            onClick: handleMenuClick,
          }}
          trigger={['click']}
          placement='bottomRight'
        >
          <Space
            style={{
              cursor: 'pointer',
              padding: `${spacing.sm} ${spacing.md}`,
              borderRadius: 8,
              transition: 'background-color 0.3s',
            }}
          >
            <Avatar
              size={40}
              icon={<UserOutlined />}
              style={{
                backgroundColor: colors.primary,
                border: `2px solid ${colors.border.default}`,
              }}
            />
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
              }}
            >
              <span
                style={{
                  color: colors.text.primary,
                  fontSize: typography.body.fontSize,
                  fontWeight: 500,
                }}
              >
                管理员
              </span>
              <span
                style={{
                  color: colors.text.secondary,
                  fontSize: typography.small.fontSize,
                }}
              >
                <EMAIL>
              </span>
            </div>
          </Space>
        </Dropdown>
      </div>
    </AntHeader>
  );
};

export default Header;
