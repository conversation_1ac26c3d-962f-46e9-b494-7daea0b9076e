/**
 * 系统设置页面
 */
import React from 'react';
import {
  Card,
  Form,
  Switch,
  Select,
  Button,
  Space,
  Divider,
  Alert,
  Modal,
} from 'antd';
import {
  SettingOutlined,
  DatabaseOutlined,
  SecurityScanOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';

import {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
} from '@styles/theme';
import { logger } from '@/utils/logger';

const { Option } = Select;

/**
 * 系统设置页面组件
 */
const Settings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);

  const handleSave = async (values: Record<string, unknown>) => {
    setLoading(true);
    try {
      // 模拟保存操作
      await new Promise(resolve => setTimeout(resolve, 1000));
      logger.log('保存设置:', values);
      Modal.success({
        title: '设置保存成功',
        content: '您的设置已成功保存并生效',
      });
    } catch (error) {
      Modal.error({
        title: '保存失败',
        content: '设置保存时发生错误，请重试',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleExportData = () => {
    Modal.info({
      title: '数据导出',
      content: '正在准备导出数据，请稍候...',
    });
  };

  const handleImportData = () => {
    Modal.warning({
      title: '数据导入',
      content: '请确保导入的数据格式正确，导入操作不可逆',
    });
  };

  const handleClearData = () => {
    Modal.confirm({
      title: '确认清空数据',
      icon: <ExclamationCircleOutlined />,
      content: '此操作将清空所有财务数据，且不可恢复！请谨慎操作。',
      okText: '确认清空',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        logger.log('清空数据');
      },
    });
  };

  return (
    <div
      style={{
        padding: 0,
        color: colors.text.primary,
      }}
    >
      {/* 页面标题 */}
      <div
        style={{
          marginBottom: spacing.xl,
        }}
      >
        <h1
          className='font-h1'
          style={{
            color: colors.text.primary,
            marginBottom: spacing.xs,
          }}
        >
          系统设置
        </h1>
        <p
          style={{
            color: colors.text.secondary,
            fontSize: typography.body.fontSize,
            margin: 0,
          }}
        >
          个性化配置和系统管理选项
        </p>
      </div>

      <div
        style={{ display: 'flex', flexDirection: 'column', gap: spacing.lg }}
      >
        {/* 基础设置 */}
        <Card
          title={
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                color: colors.text.primary,
              }}
            >
              <SettingOutlined
                style={{
                  marginRight: spacing.sm,
                  color: colors.primary,
                }}
              />
              基础设置
            </div>
          }
          bordered={false}
          style={{
            background: colors.background.card,
            border: `1px solid ${colors.border.default}`,
            borderRadius: borderRadius.large,
            boxShadow: shadows.card,
          }}
          bodyStyle={{ padding: spacing.lg }}
        >
          <Form
            form={form}
            layout='vertical'
            onFinish={handleSave}
            initialValues={{
              theme: 'dark',
              language: 'zh-CN',
              notifications: true,
              autoSave: true,
              currency: 'CNY',
              dateFormat: 'YYYY-MM-DD',
            }}
          >
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: spacing.lg,
              }}
            >
              <Form.Item
                label={
                  <span
                    style={{
                      color: colors.text.secondary,
                      fontSize: typography.small.fontSize,
                      fontWeight: 500,
                    }}
                  >
                    主题模式
                  </span>
                }
                name='theme'
              >
                <Select>
                  <Option value='light'>浅色模式</Option>
                  <Option value='dark'>深色模式</Option>
                  <Option value='auto'>跟随系统</Option>
                </Select>
              </Form.Item>

              <Form.Item
                label={
                  <span
                    style={{
                      color: colors.text.secondary,
                      fontSize: typography.small.fontSize,
                      fontWeight: 500,
                    }}
                  >
                    语言设置
                  </span>
                }
                name='language'
              >
                <Select>
                  <Option value='zh-CN'>简体中文</Option>
                  <Option value='en-US'>English</Option>
                  <Option value='zh-TW'>繁體中文</Option>
                </Select>
              </Form.Item>

              <Form.Item
                label={
                  <span
                    style={{
                      color: colors.text.secondary,
                      fontSize: typography.small.fontSize,
                      fontWeight: 500,
                    }}
                  >
                    默认货币
                  </span>
                }
                name='currency'
              >
                <Select>
                  <Option value='CNY'>人民币 (¥)</Option>
                  <Option value='USD'>美元 ($)</Option>
                  <Option value='EUR'>欧元 (€)</Option>
                  <Option value='JPY'>日元 (¥)</Option>
                </Select>
              </Form.Item>

              <Form.Item
                label={
                  <span
                    style={{
                      color: colors.text.secondary,
                      fontSize: typography.small.fontSize,
                      fontWeight: 500,
                    }}
                  >
                    日期格式
                  </span>
                }
                name='dateFormat'
              >
                <Select>
                  <Option value='YYYY-MM-DD'>2024-01-15</Option>
                  <Option value='MM/DD/YYYY'>01/15/2024</Option>
                  <Option value='DD/MM/YYYY'>15/01/2024</Option>
                </Select>
              </Form.Item>
            </div>

            <Divider
              style={{
                borderColor: colors.border.default,
                margin: `${spacing.lg} 0`,
              }}
            />

            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: spacing.lg,
              }}
            >
              <Form.Item
                label={
                  <span
                    style={{
                      color: colors.text.secondary,
                      fontSize: typography.small.fontSize,
                      fontWeight: 500,
                    }}
                  >
                    通知提醒
                  </span>
                }
                name='notifications'
                valuePropName='checked'
              >
                <Switch checkedChildren='开启' unCheckedChildren='关闭' />
              </Form.Item>

              <Form.Item
                label={
                  <span
                    style={{
                      color: colors.text.secondary,
                      fontSize: typography.small.fontSize,
                      fontWeight: 500,
                    }}
                  >
                    自动保存
                  </span>
                }
                name='autoSave'
                valuePropName='checked'
              >
                <Switch checkedChildren='开启' unCheckedChildren='关闭' />
              </Form.Item>
            </div>

            <Form.Item style={{ marginTop: spacing.xl }}>
              <Space>
                <Button
                  type='primary'
                  htmlType='submit'
                  loading={loading}
                  icon={<CheckCircleOutlined />}
                  style={{ borderRadius: borderRadius.medium }}
                >
                  保存设置
                </Button>
                <Button
                  onClick={() => form.resetFields()}
                  style={{ borderRadius: borderRadius.medium }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>

        {/* 数据管理 */}
        <Card
          title={
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                color: colors.text.primary,
              }}
            >
              <DatabaseOutlined
                style={{
                  marginRight: spacing.sm,
                  color: colors.primary,
                }}
              />
              数据管理
            </div>
          }
          bordered={false}
          style={{
            background: colors.background.card,
            border: `1px solid ${colors.border.default}`,
            borderRadius: borderRadius.large,
            boxShadow: shadows.card,
          }}
          bodyStyle={{ padding: spacing.lg }}
        >
          <Alert
            message='数据安全提醒'
            description='请定期备份您的财务数据，确保数据安全。导入和清空操作请谨慎执行。'
            type='info'
            showIcon
            style={{
              marginBottom: spacing.lg,
              background: colors.background.secondary,
              border: `1px solid ${colors.border.default}`,
              borderRadius: borderRadius.medium,
            }}
          />

          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: spacing.md,
            }}
          >
            <Button
              type='primary'
              ghost
              onClick={handleExportData}
              style={{
                height: 48,
                borderRadius: borderRadius.medium,
                borderColor: colors.success,
                color: colors.success,
              }}
            >
              导出数据
            </Button>
            <Button
              onClick={handleImportData}
              style={{
                height: 48,
                borderRadius: borderRadius.medium,
              }}
            >
              导入数据
            </Button>
            <Button
              danger
              onClick={handleClearData}
              style={{
                height: 48,
                borderRadius: borderRadius.medium,
              }}
            >
              清空数据
            </Button>
          </div>
        </Card>

        {/* 安全设置 */}
        <Card
          title={
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                color: colors.text.primary,
              }}
            >
              <SecurityScanOutlined
                style={{
                  marginRight: spacing.sm,
                  color: colors.primary,
                }}
              />
              安全设置
            </div>
          }
          bordered={false}
          style={{
            background: colors.background.card,
            border: `1px solid ${colors.border.default}`,
            borderRadius: borderRadius.large,
            boxShadow: shadows.card,
          }}
          bodyStyle={{ padding: spacing.lg }}
        >
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: spacing.md,
            }}
          >
            <Button
              style={{
                height: 48,
                borderRadius: borderRadius.medium,
              }}
            >
              修改密码
            </Button>
            <Button
              style={{
                height: 48,
                borderRadius: borderRadius.medium,
              }}
            >
              双因子认证
            </Button>
            <Button
              style={{
                height: 48,
                borderRadius: borderRadius.medium,
              }}
            >
              登录日志
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Settings;
