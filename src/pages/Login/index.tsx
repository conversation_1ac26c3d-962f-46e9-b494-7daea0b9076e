/**
 * 登录页面
 */
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Typography } from 'antd';
import styled from 'styled-components';

import PhoneAuthForm from '@components/common/PhoneAuthForm';
import { useAppDispatch, useAppSelector } from '@/hooks/redux';
import { restoreAuthState } from '@store/slices/authSlice';
import type { LoginResponse } from '@/types';
import { ROUTES } from '@constants/index';
import {
  colors,
  spacing,
  typography,
  shadows,
  borderRadius,
} from '@styles/theme';

const { Title, Text } = Typography;

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(
    135deg,
    ${colors.background.primary} 0%,
    ${colors.background.secondary} 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${spacing.lg};
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>,
    'Helvetica Neue', Arial, sans-serif;
`;

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  background-color: ${colors.background.card};
  border: 1px solid ${colors.border.default};
  border-radius: ${borderRadius.large};
  box-shadow: ${shadows.card};

  .ant-card-body {
    padding: ${spacing.xl};
  }

  @media (max-width: 768px) {
    margin: ${spacing.md};
    max-width: calc(100vw - 32px);

    .ant-card-body {
      padding: ${spacing.lg};
    }
  }
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${spacing.xxl};
`;

const Logo = styled.div`
  margin-bottom: ${spacing.md};

  h1 {
    color: ${colors.text.primary};
    font-size: ${typography.h1.fontSize};
    font-weight: ${typography.h1.fontWeight};
    margin-bottom: ${spacing.sm};
    background: linear-gradient(
      135deg,
      ${colors.primary} 0%,
      ${colors.info} 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  p {
    color: ${colors.text.secondary};
    font-size: ${typography.body.fontSize};
    margin: 0;
  }
`;

/**
 * 登录页面组件
 */
const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector(state => state.auth);

  // 组件挂载时尝试恢复认证状态
  useEffect(() => {
    dispatch(restoreAuthState());
  }, [dispatch]);

  // 如果已登录，重定向到仪表板
  useEffect(() => {
    if (isAuthenticated) {
      navigate(ROUTES.DASHBOARD, { replace: true });
    }
  }, [isAuthenticated, navigate]);

  /**
   * 处理登录成功
   */
  const handleLoginSuccess = (_response: LoginResponse) => {
    navigate(ROUTES.DASHBOARD, { replace: true });
  };

  return (
    <Container>
      <LoginCard>
        <Header>
          <Logo>
            <Title level={1}>{process.env.REACT_APP_NAME}</Title>
            <Text>{process.env.REACT_APP_DESCRIPTION}</Text>
          </Logo>
        </Header>

        <PhoneAuthForm onSuccess={handleLoginSuccess} />
      </LoginCard>
    </Container>
  );
};

export default LoginPage;
