/**
 * 仪表板页面
 */
import React from 'react';
import { Row, Col, Card, Statistic, Tag } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  TrophyOutlined,
  PieChartOutlined,
  BarChartOutlined,
} from '@ant-design/icons';

import {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
} from '@styles/theme';

/**
 * 统计卡片数据
 */
const statisticsData = [
  {
    title: '总收入',
    value: 112893.5,
    prefix: <ArrowUpOutlined />,
    valueStyle: { color: colors.success },
    icon: (
      <DollarOutlined style={{ fontSize: '24px', color: colors.success }} />
    ),
    trend: '+12.5%',
    trendType: 'up' as const,
  },
  {
    title: '总支出',
    value: 98765.3,
    prefix: <ArrowDownOutlined />,
    valueStyle: { color: colors.error },
    icon: (
      <ArrowDownOutlined style={{ fontSize: '24px', color: colors.error }} />
    ),
    trend: '+8.2%',
    trendType: 'up' as const,
  },
  {
    title: '净收益',
    value: 14128.2,
    prefix: <ArrowUpOutlined />,
    valueStyle: { color: colors.success },
    icon: (
      <TrophyOutlined style={{ fontSize: '24px', color: colors.success }} />
    ),
    trend: '+15.8%',
    trendType: 'up' as const,
  },
  {
    title: '投资回报率',
    value: 11.28,
    suffix: '%',
    prefix: <ArrowUpOutlined />,
    valueStyle: { color: colors.info },
    icon: <BarChartOutlined style={{ fontSize: '24px', color: colors.info }} />,
    trend: '+2.1%',
    trendType: 'up' as const,
  },
];

/**
 * 仪表板页面组件
 */
const Dashboard: React.FC = () => {
  return (
    <div
      style={{
        padding: 0,
        color: colors.text.primary,
      }}
    >
      {/* 页面标题 */}
      <div
        style={{
          marginBottom: spacing.xl,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <div>
          <h1
            className='font-h1'
            style={{
              color: colors.text.primary,
              marginBottom: spacing.xs,
            }}
          >
            财务仪表板
          </h1>
          <p
            style={{
              color: colors.text.secondary,
              fontSize: typography.body.fontSize,
              margin: 0,
            }}
          >
            欢迎回来！这是您的财务数据概览
          </p>
        </div>
        <Tag
          color={colors.primary}
          style={{
            padding: `${spacing.xs} ${spacing.md}`,
            fontSize: typography.small.fontSize,
            borderRadius: borderRadius.medium,
          }}
        >
          实时数据
        </Tag>
      </div>

      {/* 统计卡片区域 */}
      <Row gutter={[24, 24]} style={{ marginBottom: spacing.xl }}>
        {statisticsData.map((item, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card
              bordered={false}
              style={{
                background: colors.background.card,
                border: `1px solid ${colors.border.default}`,
                borderRadius: borderRadius.large,
                boxShadow: shadows.card,
                transition: 'all 0.3s ease',
              }}
              bodyStyle={{ padding: spacing.lg }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                  marginBottom: spacing.md,
                }}
              >
                <div style={{ flex: 1 }}>
                  <Statistic
                    title={
                      <span
                        style={{
                          color: colors.text.secondary,
                          fontSize: typography.small.fontSize,
                          fontWeight: 500,
                        }}
                      >
                        {item.title}
                      </span>
                    }
                    value={item.value}
                    precision={item.suffix === '%' ? 2 : 2}
                    valueStyle={{
                      ...item.valueStyle,
                      fontSize: typography.h3.fontSize,
                      fontWeight: typography.h3.fontWeight,
                    }}
                    prefix={item.prefix}
                    suffix={item.suffix || '¥'}
                  />
                </div>
                <div
                  style={{
                    padding: spacing.sm,
                    borderRadius: borderRadius.medium,
                    background: colors.background.secondary,
                  }}
                >
                  {item.icon}
                </div>
              </div>

              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Tag
                  color={
                    item.trendType === 'up' ? colors.success : colors.error
                  }
                  style={{
                    border: 'none',
                    borderRadius: borderRadius.small,
                    fontSize: '12px',
                  }}
                >
                  {item.trend}
                </Tag>
                <span
                  style={{
                    color: colors.text.tertiary,
                    fontSize: typography.caption.fontSize,
                  }}
                >
                  vs 上月
                </span>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card
            title={
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  color: colors.text.primary,
                }}
              >
                <TrophyOutlined
                  style={{
                    marginRight: spacing.sm,
                    color: colors.primary,
                  }}
                />
                收支趋势图
              </div>
            }
            bordered={false}
            style={{
              background: colors.background.card,
              border: `1px solid ${colors.border.default}`,
              borderRadius: borderRadius.large,
              boxShadow: shadows.card,
              height: '400px',
            }}
            bodyStyle={{
              padding: spacing.lg,
              height: 'calc(100% - 60px)',
            }}
          >
            <div
              style={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.text.tertiary,
                background: colors.background.secondary,
                borderRadius: borderRadius.medium,
                border: `2px dashed ${colors.border.default}`,
              }}
            >
              <TrophyOutlined
                style={{
                  fontSize: '48px',
                  marginBottom: spacing.md,
                  opacity: 0.5,
                }}
              />
              <span style={{ fontSize: typography.body.fontSize }}>
                图表组件待集成
              </span>
              <span
                style={{
                  fontSize: typography.small.fontSize,
                  marginTop: spacing.xs,
                  opacity: 0.7,
                }}
              >
                建议使用 ECharts 或 Chart.js
              </span>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            title={
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  color: colors.text.primary,
                }}
              >
                <PieChartOutlined
                  style={{
                    marginRight: spacing.sm,
                    color: colors.primary,
                  }}
                />
                支出分类占比
              </div>
            }
            bordered={false}
            style={{
              background: colors.background.card,
              border: `1px solid ${colors.border.default}`,
              borderRadius: borderRadius.large,
              boxShadow: shadows.card,
              height: '400px',
            }}
            bodyStyle={{
              padding: spacing.lg,
              height: 'calc(100% - 60px)',
            }}
          >
            <div
              style={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.text.tertiary,
                background: colors.background.secondary,
                borderRadius: borderRadius.medium,
                border: `2px dashed ${colors.border.default}`,
              }}
            >
              <PieChartOutlined
                style={{
                  fontSize: '48px',
                  marginBottom: spacing.md,
                  opacity: 0.5,
                }}
              />
              <span style={{ fontSize: typography.body.fontSize }}>
                饼图组件待集成
              </span>
              <span
                style={{
                  fontSize: typography.small.fontSize,
                  marginTop: spacing.xs,
                  opacity: 0.7,
                }}
              >
                展示各类支出占比
              </span>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
