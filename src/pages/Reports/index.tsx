/**
 * 报表分析页面
 */
import React from 'react';
import {
  Card,
  Table,
  DatePicker,
  Space,
  Button,
  Tag,
  Input,
  Select,
} from 'antd';
import {
  SearchOutlined,
  FileExcelOutlined,
  BarChartOutlined,
  ReloadOutlined,
} from '@ant-design/icons';

import {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
} from '@styles/theme';
import { logger } from '@/utils/logger';

const { RangePicker } = DatePicker;
const { Option } = Select;

/**
 * 表格列定义
 */
const columns = [
  {
    title: '日期',
    dataIndex: 'date',
    key: 'date',
    width: 120,
    render: (date: string) => (
      <span style={{ color: colors.text.primary }}>{date}</span>
    ),
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 80,
    render: (type: string) => (
      <Tag
        color={type === '收入' ? colors.success : colors.error}
        style={{ borderRadius: borderRadius.small }}
      >
        {type}
      </Tag>
    ),
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    align: 'right' as const,
    render: (amount: number, record: Record<string, unknown>) => (
      <span
        style={{
          color: record.type === '收入' ? colors.success : colors.error,
          fontWeight: 600,
          fontSize: typography.body.fontSize,
        }}
      >
        {record.type === '收入' ? '+' : '-'}¥{amount.toFixed(2)}
      </span>
    ),
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    width: 100,
    render: (category: string) => (
      <Tag
        style={{
          background: colors.background.secondary,
          color: colors.text.secondary,
          border: `1px solid ${colors.border.default}`,
          borderRadius: borderRadius.small,
        }}
      >
        {category}
      </Tag>
    ),
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
    render: (description: string) => (
      <span style={{ color: colors.text.secondary }}>{description}</span>
    ),
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    render: () => (
      <Space>
        <Button
          type='link'
          size='small'
          style={{ color: colors.primary, padding: 0 }}
        >
          编辑
        </Button>
        <Button type='link' size='small' danger style={{ padding: 0 }}>
          删除
        </Button>
      </Space>
    ),
  },
];

/**
 * 模拟数据
 */
const mockData = [
  {
    key: '1',
    date: '2024-01-15',
    type: '收入',
    amount: 5000,
    category: '工资',
    description: '本月工资收入',
  },
  {
    key: '2',
    date: '2024-01-14',
    type: '支出',
    amount: 1200,
    category: '餐饮',
    description: '团队聚餐费用',
  },
  {
    key: '3',
    date: '2024-01-13',
    type: '支出',
    amount: 800,
    category: '交通',
    description: '出差交通费',
  },
  {
    key: '4',
    date: '2024-01-12',
    type: '收入',
    amount: 2500,
    category: '奖金',
    description: '年终奖金',
  },
  {
    key: '5',
    date: '2024-01-11',
    type: '支出',
    amount: 300,
    category: '办公',
    description: '办公用品采购',
  },
];

/**
 * 报表分析页面组件
 */
const Reports: React.FC = () => {
  const [loading, setLoading] = React.useState(false);

  const handleSearch = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  };

  const handleExport = () => {
    logger.log('导出数据');
  };

  const handleReset = () => {
    logger.log('重置筛选条件');
  };

  return (
    <div
      style={{
        padding: 0,
        color: colors.text.primary,
      }}
    >
      {/* 页面标题 */}
      <div
        style={{
          marginBottom: spacing.xl,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <div>
          <h1
            className='font-h1'
            style={{
              color: colors.text.primary,
              marginBottom: spacing.xs,
            }}
          >
            报表分析
          </h1>
          <p
            style={{
              color: colors.text.secondary,
              fontSize: typography.body.fontSize,
              margin: 0,
            }}
          >
            财务数据详细分析与报表导出
          </p>
        </div>
        <div style={{ display: 'flex', gap: spacing.sm }}>
          <Button
            type='primary'
            icon={<BarChartOutlined />}
            style={{ borderRadius: borderRadius.medium }}
          >
            数据分析
          </Button>
          <Button
            icon={<FileExcelOutlined />}
            onClick={handleExport}
            style={{ borderRadius: borderRadius.medium }}
          >
            导出Excel
          </Button>
        </div>
      </div>

      {/* 筛选区域 */}
      <Card
        bordered={false}
        style={{
          background: colors.background.card,
          border: `1px solid ${colors.border.default}`,
          borderRadius: borderRadius.large,
          boxShadow: shadows.card,
          marginBottom: spacing.lg,
        }}
        bodyStyle={{ padding: spacing.lg }}
      >
        <div
          style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: spacing.md,
            alignItems: 'flex-end',
          }}
        >
          <div style={{ minWidth: 200 }}>
            <label
              style={{
                display: 'block',
                marginBottom: spacing.xs,
                color: colors.text.secondary,
                fontSize: typography.small.fontSize,
                fontWeight: 500,
              }}
            >
              日期范围
            </label>
            <RangePicker
              style={{ width: '100%' }}
              placeholder={['开始日期', '结束日期']}
            />
          </div>

          <div style={{ minWidth: 120 }}>
            <label
              style={{
                display: 'block',
                marginBottom: spacing.xs,
                color: colors.text.secondary,
                fontSize: typography.small.fontSize,
                fontWeight: 500,
              }}
            >
              交易类型
            </label>
            <Select
              defaultValue='all'
              style={{ width: '100%' }}
              placeholder='选择类型'
            >
              <Option value='all'>全部</Option>
              <Option value='income'>收入</Option>
              <Option value='expense'>支出</Option>
            </Select>
          </div>

          <div style={{ minWidth: 120 }}>
            <label
              style={{
                display: 'block',
                marginBottom: spacing.xs,
                color: colors.text.secondary,
                fontSize: typography.small.fontSize,
                fontWeight: 500,
              }}
            >
              分类
            </label>
            <Select
              defaultValue='all'
              style={{ width: '100%' }}
              placeholder='选择分类'
            >
              <Option value='all'>全部分类</Option>
              <Option value='salary'>工资</Option>
              <Option value='food'>餐饮</Option>
              <Option value='transport'>交通</Option>
              <Option value='office'>办公</Option>
            </Select>
          </div>

          <div style={{ minWidth: 200 }}>
            <label
              style={{
                display: 'block',
                marginBottom: spacing.xs,
                color: colors.text.secondary,
                fontSize: typography.small.fontSize,
                fontWeight: 500,
              }}
            >
              关键词搜索
            </label>
            <Input
              placeholder='搜索描述内容'
              prefix={
                <SearchOutlined style={{ color: colors.text.tertiary }} />
              }
            />
          </div>

          <div style={{ display: 'flex', gap: spacing.sm }}>
            <Button
              type='primary'
              icon={<SearchOutlined />}
              loading={loading}
              onClick={handleSearch}
              style={{ borderRadius: borderRadius.medium }}
            >
              查询
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
              style={{ borderRadius: borderRadius.medium }}
            >
              重置
            </Button>
          </div>
        </div>
      </Card>

      {/* 数据表格 */}
      <Card
        bordered={false}
        style={{
          background: colors.background.card,
          border: `1px solid ${colors.border.default}`,
          borderRadius: borderRadius.large,
          boxShadow: shadows.card,
        }}
        bodyStyle={{ padding: spacing.lg }}
        title={
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              color: colors.text.primary,
            }}
          >
            <span
              style={{
                fontSize: typography.h3.fontSize,
                fontWeight: typography.h3.fontWeight,
              }}
            >
              交易记录
            </span>
            <Tag
              style={{
                background: colors.background.secondary,
                color: colors.text.secondary,
                border: `1px solid ${colors.border.default}`,
                borderRadius: borderRadius.small,
              }}
            >
              共 {mockData.length} 条记录
            </Tag>
          </div>
        }
      >
        <Table
          columns={columns}
          dataSource={mockData}
          loading={loading}
          pagination={{
            total: mockData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          }}
          scroll={{ x: 800 }}
          style={{
            borderRadius: borderRadius.medium,
          }}
        />
      </Card>
    </div>
  );
};

export default Reports;
