import React, { useState, useEffect, useRef, createContext, useContext } from 'react';
import { Form, Input, Button, Typography, message, Card, Space, Spin } from 'antd';
import { MobileOutlined, SafetyOutlined } from '@ant-design/icons';

const { Title, Text, Link } = Typography;

// =================================
// 全局错误处理和API服务
// =================================

class ApiError extends Error {
  constructor(message, status, code) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
  }
}

const GlobalErrorHandler = {
  handle: (error, context = '') => {
    console.error(`[${context}] Error:`, error);
    
    if (error instanceof ApiError) {
      switch (error.status) {
        case 400:
          message.error(error.message || '请求参数错误');
          break;
        case 401:
          message.error('认证失败，请重新登录');
          break;
        case 403:
          message.error('没有权限执行此操作');
          break;
        case 404:
          message.error('请求的资源不存在');
          break;
        case 429:
          message.error('请求过于频繁，请稍后再试');
          break;
        case 500:
          message.error('服务器内部错误');
          break;
        default:
          message.error(error.message || '操作失败');
      }
    } else if (error.name === 'NetworkError' || error.message.includes('fetch')) {
      message.error('网络连接失败，请检查网络状态');
    } else {
      message.error(error.message || '未知错误');
    }
    
    return error;
  }
};

const httpClient = {
  baseURL: process.env.NODE_ENV === 'production' ? '' : 'http://localhost:8000',
  token: null,
  
  async request(url, options = {}) {
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(`${this.baseURL}${url}`, config);
      
      if (!response.ok) {
        let errorMessage = '请求失败';
        let errorData = null;
        
        try {
          errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || errorMessage;
        } catch (e) {
          errorMessage = response.statusText || errorMessage;
        }
        
        throw new ApiError(errorMessage, response.status, errorData?.code);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new Error('网络连接失败，请检查网络状态');
    }
  },
  
  setToken(token) {
    this.token = token;
  },
  
  getToken() {
    return this.token;
  },
  
  clearToken() {
    this.token = null;
  }
};

const AuthAPI = {
  async sendSmsCode(phone) {
    try {
      const response = await httpClient.request('/api/v1/users/send-sms-code', {
        method: 'POST',
        body: JSON.stringify({ phone }),
      });
      return response;
    } catch (error) {
      GlobalErrorHandler.handle(error, 'SendSmsCode');
      throw error;
    }
  },

  async phoneLogin(phone, code) {
    try {
      const response = await httpClient.request('/api/v1/users/phone-login', {
        method: 'POST',
        body: JSON.stringify({ phone, code }),
      });
      
      if (response.access_token) {
        httpClient.setToken(response.access_token);
      }
      
      return response;
    } catch (error) {
      GlobalErrorHandler.handle(error, 'PhoneLogin');
      throw error;
    }
  },

  logout() {
    httpClient.clearToken();
    message.success('已退出登录');
  }
};

// =================================
// 认证状态管理
// =================================

const AuthContext = createContext({
  user: null,
  token: null,
  login: () => {},
  logout: () => {},
  isAuthenticated: false,
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);

  const login = (userData, accessToken) => {
    setUser(userData);
    setToken(accessToken);
    httpClient.setToken(accessToken);
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    AuthAPI.logout();
  };

  const isAuthenticated = !!user && !!token;

  return (
    <AuthContext.Provider value={{
      user,
      token,
      login,
      logout,
      isAuthenticated,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// =================================
// 工具Hook和组件
// =================================

const useCountdown = (initialSeconds = 60) => {
  const [seconds, setSeconds] = useState(0);
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    let interval = null;
    
    if (isActive && seconds > 0) {
      interval = setInterval(() => {
        setSeconds(prevSeconds => prevSeconds - 1);
      }, 1000);
    } else if (seconds === 0) {
      setIsActive(false);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, seconds]);

  const start = () => {
    setSeconds(initialSeconds);
    setIsActive(true);
  };

  const reset = () => {
    setSeconds(0);
    setIsActive(false);
  };

  return { seconds, isActive, start, reset };
};

// 手机号验证工具
const phoneValidator = {
  isValid: (phone) => {
    return /^1[3-9]\d{9}$/.test(phone);
  },
  
  format: (phone) => {
    // 格式化显示：138 0013 8000
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
  },
  
  getCarrier: (phone) => {
    const prefix = phone.substring(0, 3);
    const carriers = {
      '134': '中国移动', '135': '中国移动', '136': '中国移动', '137': '中国移动',
      '138': '中国移动', '139': '中国移动', '147': '中国移动', '150': '中国移动',
      '151': '中国移动', '152': '中国移动', '157': '中国移动', '158': '中国移动',
      '159': '中国移动', '178': '中国移动', '182': '中国移动', '183': '中国移动',
      '184': '中国移动', '187': '中国移动', '188': '中国移动', '198': '中国移动',
      '130': '中国联通', '131': '中国联通', '132': '中国联通', '145': '中国联通',
      '155': '中国联通', '156': '中国联通', '166': '中国联通', '175': '中国联通',
      '176': '中国联通', '185': '中国联通', '186': '中国联通',
      '133': '中国电信', '149': '中国电信', '153': '中国电信', '173': '中国电信',
      '177': '中国电信', '180': '中国电信', '181': '中国电信', '189': '中国电信',
      '199': '中国电信'
    };
    return carriers[prefix] || '未知运营商';
  }
};

// 6位验证码输入组件
const SmsCodeInput = ({ value, onChange, onComplete, disabled }) => {
  const inputRefs = useRef([]);
  const [codes, setCodes] = useState(['', '', '', '', '', '']);

  useEffect(() => {
    if (value && value.length <= 6) {
      const newCodes = value.split('').concat(Array(6 - value.length).fill(''));
      setCodes(newCodes.slice(0, 6));
    }
  }, [value]);

  const handleInputChange = (index, val) => {
    // 只允许输入数字
    const digit = val.replace(/\D/g, '');
    if (digit.length > 1) return;

    const newCodes = [...codes];
    newCodes[index] = digit;
    setCodes(newCodes);

    const newValue = newCodes.join('');
    onChange(newValue);

    // 自动移动到下一个输入框
    if (digit && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // 检查是否完成输入
    if (newValue.length === 6) {
      onComplete?.(newValue);
    }
  };

  const handleKeyDown = (index, e) => {
    // 处理退格键
    if (e.key === 'Backspace') {
      e.preventDefault();
      const newCodes = [...codes];
      
      if (codes[index]) {
        // 当前位置有值，清除当前位置
        newCodes[index] = '';
      } else if (index > 0) {
        // 当前位置无值，移动到上一个位置并清除
        newCodes[index - 1] = '';
        inputRefs.current[index - 1]?.focus();
      }
      
      setCodes(newCodes);
      onChange(newCodes.join(''));
    }
    
    // 处理左右箭头键
    if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
    if (e.key === 'ArrowRight' && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedText = (e.clipboardData || window.clipboardData).getData('text');
    const digits = pastedText.replace(/\D/g, '').slice(0, 6);
    
    if (digits.length > 0) {
      const newCodes = digits.split('').concat(Array(6 - digits.length).fill(''));
      setCodes(newCodes.slice(0, 6));
      onChange(digits);
      
      // 聚焦到最后一个输入的位置
      const nextIndex = Math.min(digits.length, 5);
      inputRefs.current[nextIndex]?.focus();
      
      if (digits.length === 6) {
        onComplete?.(digits);
      }
    }
  };

  return (
    <div style={{ 
      display: 'flex', 
      gap: '12px', 
      justifyContent: 'center',
      width: '100%'
    }}>
      {codes.map((code, index) => (
        <input
          key={index}
          ref={el => inputRefs.current[index] = el}
          type="text"
          value={code}
          onChange={(e) => handleInputChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          onPaste={handlePaste}
          disabled={disabled}
          maxLength={1}
          style={{
            width: '48px',
            height: '56px',
            backgroundColor: '#1a1a1a',
            border: `2px solid ${code ? '#4f46e5' : '#333'}`,
            borderRadius: '8px',
            color: '#fff',
            fontSize: '24px',
            fontWeight: '600',
            textAlign: 'center',
            outline: 'none',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            cursor: disabled ? 'not-allowed' : 'text',
            opacity: disabled ? 0.6 : 1
          }}
          onFocus={(e) => {
            if (!disabled) {
              e.target.style.borderColor = '#4f46e5';
              e.target.style.boxShadow = '0 0 0 3px rgba(79, 70, 229, 0.1)';
            }
          }}
          onBlur={(e) => {
            e.target.style.borderColor = code ? '#4f46e5' : '#333';
            e.target.style.boxShadow = 'none';
          }}
        />
      ))}
    </div>
  );
};

// =================================
// 主要组件
// =================================

const PhoneAuthForm = ({ onSuccess }) => {
  const [step, setStep] = useState(1); // 1: 手机号输入, 2: 验证码输入
  const [phone, setPhone] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [phoneError, setPhoneError] = useState('');
  const [loginLoading, setLoginLoading] = useState(false);
  const [smsLoading, setSmsLoading] = useState(false);
  const { seconds, isActive, start } = useCountdown(60);

  // 手机号输入处理
  const handlePhoneChange = (e) => {
    const value = e.target.value.replace(/\D/g, ''); // 只保留数字
    if (value.length <= 11) {
      setPhone(value);
      setPhoneError('');
    }
  };

  // 手机号验证
  const validatePhone = () => {
    if (!phone) {
      setPhoneError('请输入手机号');
      return false;
    }
    if (!phoneValidator.isValid(phone)) {
      setPhoneError('请输入正确的手机号格式');
      return false;
    }
    setPhoneError('');
    return true;
  };

  // 发送验证码
  const handleSendSms = async () => {
    if (!validatePhone()) return;
    
    try {
      setSmsLoading(true);
      await AuthAPI.sendSmsCode(phone);
      message.success('验证码已发送到您的手机');
      setStep(2);
      start();
    } catch (error) {
      // 错误已在GlobalErrorHandler中处理
    } finally {
      setSmsLoading(false);
    }
  };

  // 验证码输入完成
  const handleSmsCodeComplete = async (code) => {
    setSmsCode(code);
    await handleLogin(code);
  };

  // 提交登录
  const handleLogin = async (code = smsCode) => {
    if (code.length !== 6) {
      message.error('请输入完整的验证码');
      return;
    }

    try {
      setLoginLoading(true);
      const response = await AuthAPI.phoneLogin(phone, code);
      message.success('登录成功');
      
      if (onSuccess) {
        onSuccess(response);
      }
    } catch (error) {
      // 错误已在GlobalErrorHandler中处理
      setSmsCode('');
    } finally {
      setLoginLoading(false);
    }
  };

  // 重新发送验证码
  const handleResendSms = async () => {
    try {
      setSmsLoading(true);
      await AuthAPI.sendSmsCode(phone);
      message.success('验证码已重新发送');
      start();
    } catch (error) {
      // 错误已在GlobalErrorHandler中处理
    } finally {
      setSmsLoading(false);
    }
  };

  // 返回手机号输入步骤
  const handleBackToPhone = () => {
    setStep(1);
    setSmsCode('');
  };

  return (
    <div>
      {step === 1 ? (
        // 手机号输入步骤
        <div>
          <div style={{ marginBottom: '24px' }}>
            <Text style={{ color: '#a3a3a3', fontSize: '16px', display: 'block', marginBottom: '8px' }}>
              手机号
            </Text>
            <Input
              prefix={<MobileOutlined style={{ color: '#a3a3a3' }} />}
              placeholder="请输入手机号"
              value={phone}
              onChange={handlePhoneChange}
              onPressEnter={handleSendSms}
              size="large"
              status={phoneError ? 'error' : ''}
              style={{
                backgroundColor: '#1a1a1a',
                border: `2px solid ${phoneError ? '#ef4444' : '#333'}`,
                color: '#fff',
                fontSize: '16px',
                height: '56px',
                borderRadius: '8px'
              }}
            />
            {phoneError && (
              <Text style={{ color: '#ef4444', fontSize: '14px', marginTop: '4px', display: 'block' }}>
                {phoneError}
              </Text>
            )}
            {phone && phoneValidator.isValid(phone) && (
              <Text style={{ color: '#10b981', fontSize: '14px', marginTop: '4px', display: 'block' }}>
                {phoneValidator.getCarrier(phone)} • {phoneValidator.format(phone)}
              </Text>
            )}
          </div>

          <Button
            type="primary"
            loading={smsLoading}
            onClick={handleSendSms}
            disabled={!phone || !!phoneError}
            block
            size="large"
            style={{
              height: '56px',
              backgroundColor: '#4f46e5',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: '500',
              marginBottom: '24px'
            }}
          >
            {smsLoading ? '发送中...' : '获取验证码'}
          </Button>
        </div>
      ) : (
        // 验证码输入步骤
        <div>
          <div style={{ marginBottom: '32px', textAlign: 'center' }}>
            <Text style={{ color: '#a3a3a3', fontSize: '16px', display: 'block', marginBottom: '8px' }}>
              验证码已发送至
            </Text>
            <Text style={{ color: '#fff', fontSize: '18px', fontWeight: '500' }}>
              {phoneValidator.format(phone)}
            </Text>
            <Button 
              type="link" 
              onClick={handleBackToPhone}
              style={{ color: '#4f46e5', padding: '0', fontSize: '14px', marginLeft: '8px' }}
            >
              修改
            </Button>
          </div>

          <div style={{ marginBottom: '32px' }}>
            <Text style={{ color: '#a3a3a3', fontSize: '16px', display: 'block', marginBottom: '16px', textAlign: 'center' }}>
              请输入6位验证码
            </Text>
            <SmsCodeInput
              value={smsCode}
              onChange={setSmsCode}
              onComplete={handleSmsCodeComplete}
              disabled={loginLoading}
            />
          </div>

          <div style={{ textAlign: 'center', marginBottom: '24px' }}>
            {isActive ? (
              <Text style={{ color: '#a3a3a3', fontSize: '14px' }}>
                {seconds}秒后可重新发送
              </Text>
            ) : (
              <Button
                type="link"
                onClick={handleResendSms}
                loading={smsLoading}
                style={{ color: '#4f46e5', padding: '0', fontSize: '14px' }}
              >
                重新发送验证码
              </Button>
            )}
          </div>

          {loginLoading && (
            <div style={{ textAlign: 'center', marginBottom: '16px' }}>
              <Space>
                <Spin size="small" />
                <Text style={{ color: '#a3a3a3' }}>登录中...</Text>
              </Space>
            </div>
          )}
        </div>
      )}

      {/* 用户协议 */}
      <div style={{ 
        textAlign: 'center', 
        color: '#666', 
        fontSize: '14px',
        lineHeight: '20px',
        marginTop: '16px'
      }}>
        {step === 1 ? '获取验证码' : '登录'}表示同意{' '}
        <Link style={{ color: '#4f46e5' }}>用户协议</Link>
        {' '}和{' '}
        <Link style={{ color: '#4f46e5' }}>隐私政策</Link>
      </div>
    </div>
  );
};

const UserDashboard = ({ user, onLogout }) => {
  return (
    <div style={{ textAlign: 'center' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div>
          <Title level={3} style={{ color: '#fff', marginBottom: '8px' }}>
            欢迎使用 FinSight
          </Title>
          <Text style={{ color: '#a3a3a3', fontSize: '16px' }}>
            {user.phone ? phoneValidator.format(user.phone) : '用户已登录'}
          </Text>
        </div>
        
        <Button 
          onClick={onLogout}
          size="large"
          style={{
            backgroundColor: '#333',
            border: '1px solid #555',
            color: '#fff',
            borderRadius: '8px',
            height: '48px',
            width: '120px'
          }}
        >
          退出登录
        </Button>
      </Space>
    </div>
  );
};

const FinSightAuth = () => {
  const { user, login, logout, isAuthenticated } = useAuth();

  const handleLoginSuccess = (response) => {
    const { access_token, user: userData, ...otherData } = response;
    
    const userInfo = userData || { 
      phone: otherData.phone,
      id: otherData.user_id,
      ...otherData 
    };
    
    login(userInfo, access_token);
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    }}>
      <Card style={{
        width: '100%',
        maxWidth: '400px',
        backgroundColor: '#111',
        border: '1px solid #333',
        borderRadius: '16px',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4)',
        padding: '32px'
      }}>
        {/* Logo和标题 */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <Title level={2} style={{ color: '#fff', marginBottom: '8px', fontWeight: '600' }}>
            FinSight
          </Title>
          <Text style={{ color: '#a3a3a3', fontSize: '16px' }}>
            金融信息智能推送系统
          </Text>
        </div>

        {/* 主要内容 */}
        {isAuthenticated ? (
          <UserDashboard user={user} onLogout={logout} />
        ) : (
          <PhoneAuthForm onSuccess={handleLoginSuccess} />
        )}
      </Card>

      {/* 全局样式 */}
      <style jsx global>{`
        .ant-input {
          background-color: #1a1a1a !important;
          border-color: #333 !important;
          color: #fff !important;
        }
        
        .ant-input:hover {
          border-color: #4f46e5 !important;
        }
        
        .ant-input:focus,
        .ant-input-focused {
          border-color: #4f46e5 !important;
          box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
        }
        
        .ant-input::placeholder {
          color: #555 !important;
        }
        
        .ant-input-affix-wrapper {
          background-color: #1a1a1a !important;
          border-color: #333 !important;
        }
        
        .ant-input-affix-wrapper:hover {
          border-color: #4f46e5 !important;
        }
        
        .ant-input-affix-wrapper-focused {
          border-color: #4f46e5 !important;
          box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
        }
        
        .ant-btn:hover {
          transform: translateY(-1px);
        }
        
        .ant-message {
          z-index: 9999;
        }
        
        @media (max-width: 768px) {
          .ant-card {
            margin: 16px !important;
            max-width: calc(100vw - 32px) !important;
          }
        }
      `}</style>
    </div>
  );
};

export default function App() {
  return (
    <AuthProvider>
      <FinSightAuth />
    </AuthProvider>
  );
}

export {
  AuthAPI,
  httpClient,
  GlobalErrorHandler,
  useAuth,
  AuthProvider,
  ApiError,
  phoneValidator
};