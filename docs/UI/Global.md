# FinSight UI设计规范

## 设计原则

## 色彩系统

### 主色调
- **背景色**: `#0c0c0c` (主背景) / `#1a1a1a` (次级背景)
- **卡片背景**: `#111111` 
- **边框色**: `#333333` (默认) / `#555555` (悬停)

### 主题色
- **主要**: `#4f46e5` (品牌蓝)
- **成功**: `#10b981` 
- **警告**: `#f59e0b`
- **错误**: `#ef4444`
- **信息**: `#3b82f6`

### 文字色彩
- **主文字**: `#ffffff`
- **次要文字**: `#a3a3a3`
- **辅助文字**: `#666666`
- **占位符**: `#555555`

## 字体系统

### 字号规范
- **标题1**: `28px` / `font-weight: 600`
- **标题2**: `24px` / `font-weight: 600`
- **标题3**: `20px` / `font-weight: 500`
- **正文**: `16px` / `font-weight: 400`
- **小文字**: `14px` / `font-weight: 400`
- **辅助文字**: `12px` / `font-weight: 400`

## 间距系统

### 基础间距单位: 8px
- **xs**: `4px`
- **sm**: `8px`
- **md**: `16px`  
- **lg**: `24px`
- **xl**: `32px`
- **xxl**: `48px`

## 组件规范

### 按钮
- **高度**: `48px` (大) / `40px` (中) / `32px` (小)
- **圆角**: `8px`
- **主要按钮**: 背景 `#4f46e5`，文字 `#ffffff`
- **次要按钮**: 背景 `#333333`，边框 `1px solid #555555`

### 输入框
- **高度**: `48px` (大) / `40px` (中) / `32px` (小)
- **背景**: `#1a1a1a`
- **边框**: `1px solid #333333`
- **聚焦边框**: `1px solid #4f46e5`
- **圆角**: `6px`

### 卡片
- **背景**: `#111111`
- **边框**: `1px solid #333333`
- **圆角**: `16px`
- **阴影**: `0 8px 32px rgba(0, 0, 0, 0.4)`

### 表单
- **标签颜色**: `#a3a3a3`
- **标签间距**: `8px`
- **表单项间距**: `24px`

## 响应式断点

- **mobile**: `< 768px`
- **tablet**: `768px - 1024px`
- **desktop**: `> 1024px`

## 动画规范

### 过渡时间
- **快速**: `150ms`
- **标准**: `300ms`
- **慢速**: `500ms`

### 缓动函数
- **标准**: `cubic-bezier(0.4, 0, 0.2, 1)`
- **进入**: `cubic-bezier(0, 0, 0.2, 1)`
- **退出**: `cubic-bezier(0.4, 0, 1, 1)`

## 图标规范

- **大小**: `16px` / `20px` / `24px`
- **颜色**: 继承文字颜色
- **间距**: 与文字间距 `8px`

## 状态规范

### 禁用状态
- **背景**: `#1a1a1a`
- **文字**: `#555555`
- **透明度**: `0.6`

### 悬停状态
- **边框颜色**: `#4f46e5`
- **背景亮度**: `+10%`

### 聚焦状态
- **边框**: `2px solid #4f46e5`
- **阴影**: `0 0 0 3px rgba(79, 70, 229, 0.1)`