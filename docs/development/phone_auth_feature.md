# 手机号+验证码登录功能

## 功能概述

FinSight 金融信息智能推送系统采用基于手机号+短信验证码的认证方式，摒弃了传统的用户名+密码登录模式，提供更安全、便捷的用户体验。

## 功能特性

### 核心功能
- ✅ 手机号格式验证和运营商识别
- ✅ 短信验证码发送和验证
- ✅ 自动token刷新机制
- ✅ 认证状态持久化
- ✅ 路由保护
- ✅ 响应式设计

### 用户体验优化
- ✅ 6位数字验证码输入组件
- ✅ 自动跳转和粘贴支持
- ✅ 倒计时重发功能
- ✅ 键盘导航支持
- ✅ 掩码显示手机号
- ✅ 实时表单验证

### 安全特性
- ✅ JWT token自动刷新
- ✅ Token过期检查
- ✅ 并发刷新保护
- ✅ 错误状态处理
- ✅ 会话管理

## 技术架构

### 文件结构
```
src/
├── components/common/
│   ├── SmsCodeInput/          # 短信验证码输入组件
│   └── PhoneAuthForm/         # 手机号认证表单组件
├── services/
│   ├── api.ts                 # API客户端配置
│   └── authService.ts         # 认证服务
├── store/slices/
│   └── authSlice.ts           # 认证状态管理
├── utils/
│   └── phoneValidator.ts      # 手机号验证工具
├── hooks/
│   ├── useCountdown.ts        # 倒计时Hook
│   └── redux.ts               # Redux TypeScript Hooks
├── pages/
│   └── Login/                 # 登录页面
└── routes/
    ├── AppRouter.tsx          # 应用路由配置
    └── ProtectedRoute.tsx     # 路由保护组件
```

### 核心组件

#### 1. SmsCodeInput 组件
6位数字验证码输入组件，支持：
- 自动聚焦和跳转
- 键盘导航（方向键、退格键）
- 粘贴识别和处理
- 输入验证（只允许数字）
- 完成回调

#### 2. PhoneAuthForm 组件
手机号认证表单，包含两个步骤：
- 步骤1：手机号输入和验证
- 步骤2：验证码输入和登录

#### 3. TokenManager 类
Token管理工具，提供：
- Token存储和读取
- 过期时间检查
- 自动刷新机制
- 并发控制

### 状态管理

使用 Redux Toolkit 管理认证状态：

```typescript
interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  accessToken: string | null;
  refreshToken: string | null;
  tokenExpiresAt: number | null;
  loading: boolean;
  error: string | null;
}
```

### API 接口

基于后端API文档实现的认证接口：
- `POST /api/v1/users/send-sms-code` - 发送短信验证码
- `POST /api/v1/users/phone-login` - 手机号登录
- `POST /api/v1/users/refresh-token` - 刷新访问令牌
- `GET /api/v1/users/me` - 获取当前用户信息
- `POST /api/v1/users/logout` - 用户登出

## 使用方法

### 1. 基本使用

```tsx
import PhoneAuthForm from '@components/common/PhoneAuthForm';

const LoginPage = () => {
  const handleLoginSuccess = (response) => {
    console.log('登录成功:', response.user);
    // 重定向到仪表板
  };

  return (
    <PhoneAuthForm onSuccess={handleLoginSuccess} />
  );
};
```

### 2. 路由保护

```tsx
import ProtectedRoute from '@routes/ProtectedRoute';

<Route path="/dashboard" element={
  <ProtectedRoute>
    <Dashboard />
  </ProtectedRoute>
} />
```

### 3. 认证状态管理

```tsx
import { useAppSelector, useAppDispatch } from '@/hooks/redux';
import { logout } from '@store/slices/authSlice';

const Header = () => {
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();

  const handleLogout = () => {
    dispatch(logout());
  };

  return (
    <div>
      {isAuthenticated && (
        <div>
          <span>欢迎, {user?.phone}</span>
          <button onClick={handleLogout}>退出</button>
        </div>
      )}
    </div>
  );
};
```

## 配置说明

### 常量配置

```typescript
// src/constants/index.ts
export const AUTH = {
  SMS_CODE_EXPIRES: 300,         // 验证码有效期5分钟
  SMS_RESEND_COUNTDOWN: 60,      // 重发倒计时60秒
  TOKEN_REFRESH_THRESHOLD: 300,  // token刷新阈值5分钟
  MAX_LOGIN_ATTEMPTS: 3,         // 最大登录尝试次数
};
```

### 环境变量

```bash
# .env
REACT_APP_API_BASE_URL=http://localhost:8000
```

## 测试

### 运行测试

```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

### 测试覆盖

项目包含完整的单元测试：
- 手机号验证工具测试
- 短信验证码输入组件测试
- 认证服务API测试
- Redux状态管理测试

## 安全考虑

### Token管理
- 访问令牌有效期30分钟，自动刷新
- 刷新令牌有效期7天
- 令牌存储在localStorage中
- 支持并发请求的token刷新

### 错误处理
- 网络错误自动重试
- API错误统一处理
- 用户友好的错误提示
- 自动清理无效认证状态

### 输入验证
- 手机号格式验证
- 短信验证码长度和格式验证
- 防止非数字输入
- XSS防护

## 性能优化

### 组件优化
- 使用React.memo避免不必要渲染
- useCallback缓存回调函数
- 懒加载页面组件
- 代码分割

### 网络优化
- 请求超时配置
- 自动重试机制
- Token预刷新
- 并发控制

## 浏览器兼容性

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 已知问题

1. iOS Safari中的输入框聚焦问题已通过inputMode="numeric"解决
2. 某些Android设备的粘贴事件兼容性已处理
3. 深色主题下的输入框样式已优化

## 未来优化计划

1. 支持生物识别登录（指纹/面容）
2. 短信验证码模板优化
3. 多语言支持
4. 无障碍功能增强
5. 离线状态处理

## 参考文档

- [后端API文档](../user/backend_api.md)
- [UI设计规范](../UI/Global.md)
- [前端架构文档](../user/frontend.md) 