# 安全配置与漏洞修复指南

## 概述

本文档提供了项目安全配置、漏洞检测和修复的完整指南。

## 安全扫描工具

### 1. npm audit

项目使用 `npm audit` 进行依赖包安全漏洞检测：

```bash
# 检查所有漏洞
npm audit

# 仅显示高危漏洞
npm audit --audit-level=high

# 生成JSON格式报告
npm audit --json
```

### 2. 自定义安全修复脚本

项目提供了自动化安全修复脚本：

```bash
# 检查当前安全状态
npm run security:check

# 自动修复安全漏洞
npm run security:fix

# 生成详细安全报告
npm run security:report
```

## 常见安全漏洞及修复方案

### 1. nth-check 正则表达式复杂度漏洞

**漏洞描述**: nth-check < 2.0.1 存在正则表达式复杂度漏洞 (CVE-2021-3803)

**影响**: 可能导致拒绝服务攻击

**修复方案**:
```bash
npm install nth-check@^2.1.1
```

### 2. PostCSS 解析错误漏洞

**漏洞描述**: postcss < 8.4.31 存在行返回解析错误漏洞

**影响**: 可能导致代码注入

**修复方案**:
```bash
npm install postcss@^8.4.31
```

### 3. webpack-dev-server 源码泄露漏洞

**漏洞描述**: webpack-dev-server <= 5.2.0 存在源码泄露风险

**影响**: 在非Chrome浏览器中访问恶意网站时可能泄露源码

**修复方案**:
```bash
npm install webpack-dev-server@^5.2.1
```

## package.json 安全配置

### 依赖覆盖 (overrides)

使用 `overrides` 字段强制指定安全版本：

```json
{
  "overrides": {
    "nth-check": "^2.1.1",
    "postcss": "^8.4.31",
    "webpack-dev-server": "^5.2.1"
  }
}
```

### 安全脚本

```json
{
  "scripts": {
    "security:check": "npm audit",
    "security:fix": "node scripts/security-fix.js fix",
    "security:report": "node scripts/security-fix.js report"
  }
}
```

## CI/CD 安全流程

### 生产环境安全检查

CI/CD 流程包含以下安全检查：

1. **自动漏洞修复**: 尝试自动修复已知漏洞
2. **高危漏洞阻断**: 发现高危或严重漏洞时阻止部署
3. **敏感信息检测**: 扫描源码中的敏感信息
4. **安全报告生成**: 生成详细的安全报告

### 部署前安全检查清单

- [ ] 运行 `npm run security:check` 检查漏洞
- [ ] 运行 `npm run security:fix` 修复漏洞
- [ ] 确保没有高危或严重漏洞
- [ ] 检查源码中无敏感信息泄露
- [ ] 验证所有依赖都是可信来源

## 敏感信息检测规则

### 检测模式

1. **明文密码**: `password.*=.*['"][^'"]*['"]`
2. **密钥信息**: `secret.*=.*['"][^'"]*['"]`
3. **令牌信息**: `token.*=.*['"][^'"]*['"]`
4. **API密钥**: `(api[_-]?key|access[_-]?key|private[_-]?key).*=.*['"][a-zA-Z0-9]{20,}['"]`

### 避免误报

- 测试文件自动排除 (`*.test.*`, `*.spec.*`)
- 使用环境变量替代硬编码敏感信息
- 敏感配置使用加密存储

## 安全最佳实践

### 1. 依赖管理

- 定期更新依赖包到最新稳定版本
- 使用 `npm audit` 定期检查漏洞
- 避免使用已知有漏洞的包版本
- 使用 `package-lock.json` 锁定依赖版本

### 2. 代码安全

- 不在源码中硬编码敏感信息
- 使用环境变量管理配置
- 实施输入验证和输出编码
- 定期进行代码安全审查

### 3. 部署安全

- 使用HTTPS传输
- 实施适当的CORS策略
- 配置安全响应头
- 定期更新服务器和依赖

### 4. 监控与响应

- 设置安全监控告警
- 建立安全事件响应流程
- 定期进行安全评估
- 保持安全知识更新

## 故障排除

### 常见问题

1. **npm audit 失败**
   - 检查网络连接
   - 更新npm到最新版本
   - 清理npm缓存: `npm cache clean --force`

2. **依赖冲突**
   - 使用 `npm ls` 检查依赖树
   - 使用 `overrides` 解决版本冲突
   - 考虑使用 `resolutions` (yarn)

3. **修复脚本失败**
   - 检查Node.js版本兼容性
   - 确保有写入权限
   - 查看详细错误日志

### 紧急修复流程

1. 立即识别受影响的组件
2. 评估安全风险等级
3. 应用临时缓解措施
4. 开发和测试永久修复方案
5. 部署修复并验证效果
6. 更新文档和流程

## 联系与支持

如果遇到安全相关问题，请：

1. 优先通过内部安全渠道报告
2. 不要在公开渠道披露漏洞细节
3. 提供完整的重现步骤和环境信息
4. 关注官方安全公告和更新

---

**注意**: 安全是一个持续的过程，需要定期评估和改进。请确保团队成员都了解并遵循这些安全实践。 