# CI/CD 完整配置指南

本文档提供了 FinSight 前端应用完整的 CI/CD 配置、部署和管理指南。

## 📋 目录

1. [概述](#概述)
2. [工作流配置](#工作流配置)
3. [环境变量与密钥配置](#环境变量与密钥配置)
4. [部署架构](#部署架构)
5. [自动化部署](#自动化部署)
6. [手动部署](#手动部署)
7. [服务器配置](#服务器配置)
8. [故障排除](#故障排除)
9. [性能优化](#性能优化)
10. [安全最佳实践](#安全最佳实践)
11. [维护计划](#维护计划)

## 概述

FinSight 前端应用使用 GitHub Actions 实现持续集成和持续部署 (CI/CD)，支持开发环境和生产环境的自动化部署。项目采用加密的环境配置文件来保护敏感信息，确保部署过程的安全性。

### 技术栈
- **CI/CD**: GitHub Actions
- **部署方式**: SSH + 脚本部署
- **Web 服务器**: Nginx
- **环境管理**: 加密环境配置文件
- **版本控制**: Git (develop/main 分支策略)

## 工作流配置

### 📁 工作流文件结构

```
.github/workflows/
├── develop.yml    # 开发环境工作流
├── main.yml       # 生产环境工作流
└── rollback.yml   # 回滚工作流
```

### 🔄 工作流详情

#### 1. 开发环境工作流 (develop.yml)

**触发条件:**
- 推送到 `develop` 分支
- 针对 `develop` 分支的 Pull Request

**工作流程:**
1. **代码质量检查和测试**
   - TypeScript 类型检查
   - ESLint 代码检查
   - Prettier 格式检查
   - 单元测试执行

2. **构建开发环境应用**
   - 解密开发环境配置文件
   - 构建应用
   - 压缩并上传构建产物

3. **部署到开发环境**
   - 下载构建产物
   - 开发环境健康检查
   - 部署到开发服务器
   - 创建开发环境发布标签 (`dev-v{run_number}`)

**特点:**
- 相对宽松的检查标准
- 快速反馈开发过程
- 自动部署到开发环境

#### 2. 生产环境工作流 (main.yml)

**触发条件:**
- 推送到 `main` 分支
- 针对 `main` 分支的 Pull Request

**工作流程:**
1. **代码质量检查和测试**
   - 基础的质量检查（同开发环境）
   - **额外的生产环境安全扫描**
     - npm audit 安全漏洞检查
     - 源码敏感信息检查

2. **构建生产环境应用**
   - 解密生产环境配置文件（必须设置）
   - 构建应用
   - **生产构建质量检查**
     - 构建产物大小检查
     - 调试信息清理检查
     - 关键文件完整性检查

3. **部署到生产环境**
   - 下载构建产物
   - **生产环境最终安全检查**
   - **创建部署备份**
   - 部署到生产服务器
   - **生产环境健康检查**
   - 创建 GitHub Release (`v{run_number}-prod`)

**特点:**
- 严格的安全检查
- 多层质量保证
- 自动备份机制
- 详细的发布记录

## 环境变量与密钥配置

### 🔐 GitHub Secrets 配置

#### 必需的加密密钥

**开发环境密钥:**
- **名称**: `DEV_ENCRYPTION_KEY`
- **说明**: 用于解密 `.env.development.enc` 文件的密钥
- **来源**: 与本地加密时使用的密钥相同

**生产环境密钥:**
- **名称**: `PROD_ENCRYPTION_KEY`
- **说明**: 用于解密 `.env.production.enc` 文件的密钥
- **来源**: 与本地加密时使用的密钥相同

#### 服务器配置密钥

**开发环境所需 Secrets:**

```yaml
DEV_ENCRYPTION_KEY    # 开发环境配置文件加密密钥（可选）
DEV_SSH_KEY          # 开发服务器SSH私钥
DEV_HOST             # 开发服务器地址
DEV_USERNAME         # 开发服务器用户名
DEV_DEPLOY_PATH      # 开发环境部署路径
```

**生产环境所需 Secrets:**

```yaml
PROD_ENCRYPTION_KEY     # 生产环境配置文件加密密钥（必须）
PROD_SSH_KEY           # 生产服务器SSH私钥
PROD_HOST              # 生产服务器地址
PROD_USERNAME          # 生产服务器用户名
PROD_DEPLOY_PATH       # 生产环境部署路径
PROD_HEALTH_CHECK_URL  # 生产环境健康检查URL（可选）
GITHUB_TOKEN           # GitHub访问令牌（自动提供）
```

### 📋 GitHub Secrets 设置步骤

#### 1. 获取加密密钥

```bash
# 如果你有现有的加密文件，可以尝试用这个脚本测试密钥
./scripts/debug-encryption.sh check

# 或者重新生成密钥（建议16位以上随机字符串）
openssl rand -base64 32
```

#### 2. 在GitHub中设置Secrets

1. 打开你的GitHub仓库
2. 点击 `Settings` 标签
3. 在左侧菜单中选择 `Secrets and variables` → `Actions`
4. 点击 `New repository secret`
5. 添加每个必需的secret

#### 3. 本地加密流程

```bash
# 设置环境变量
export DEV_ENCRYPTION_KEY="your-dev-key"
export PROD_ENCRYPTION_KEY="your-prod-key"

# 运行加密脚本
./job_before_push.sh
```

或者手动加密：

```bash
# 加密开发环境文件
openssl aes-256-cbc -a -pbkdf2 -iter 100000 \
  -in .env.development \
  -out .env.development.enc \
  -k "your-dev-key"

# 加密生产环境文件
openssl aes-256-cbc -a -pbkdf2 -iter 100000 \
  -in .env.production \
  -out .env.production.enc \
  -k "your-prod-key"
```

## 部署架构

### 环境说明

- **开发环境 (Development)**: 用于开发测试，自动部署 `develop` 分支
- **生产环境 (Production)**: 正式环境，自动部署 `main` 分支

### 部署方式

1. **自动化部署**: 通过 GitHub Actions 自动触发
2. **手动部署**: 使用部署脚本手动执行
3. **回滚部署**: 快速回滚到上一个版本

### 部署目录结构

```
/var/www/app/
├── releases/           # 历史版本目录
│   ├── 20241218_143022 # 版本时间戳目录
│   ├── 20241218_151045
│   └── ...
├── shared/             # 共享资源目录
├── current -> releases/20241218_151045  # 当前版本软链接
└── previous -> releases/20241218_143022 # 前一版本软链接（用于回滚）
```

## 自动化部署

### 🚀 使用指南

#### 开发流程

1. **功能开发**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/your-feature-name
   # 进行开发
   git add .
   git commit -m "feat: add your feature"
   git push origin feature/your-feature-name
   ```

2. **创建 Pull Request**
   - 目标分支：`develop`
   - 触发代码质量检查

3. **合并到 develop**
   - PR 合并后自动触发开发环境部署

#### 生产发布流程

1. **创建发布 PR**
   ```bash
   git checkout main
   git pull origin main
   git checkout -b release/v1.x.x
   git merge develop
   git push origin release/v1.x.x
   ```

2. **创建 Pull Request**
   - 目标分支：`main`
   - 触发完整的生产环境检查

3. **合并到 main**
   - PR 合并后自动触发生产环境部署
   - 自动创建 GitHub Release

## 手动部署

### 使用部署脚本

#### 基本用法

```bash
# 部署到开发环境
./scripts/deploy.sh deploy \
  dev.example.com \
  deploy \
  ~/.ssh/id_rsa \
  /var/www/dev \
  development

# 部署到生产环境
./scripts/deploy.sh deploy \
  prod.example.com \
  deploy \
  ~/.ssh/id_rsa \
  /var/www/prod \
  production
```

#### 回滚部署

```bash
# 回滚开发环境
./scripts/deploy.sh rollback \
  dev.example.com \
  deploy \
  ~/.ssh/id_rsa \
  /var/www/dev

# 回滚生产环境
./scripts/deploy.sh rollback \
  prod.example.com \
  deploy \
  ~/.ssh/id_rsa \
  /var/www/prod
```

### 本地测试

```bash
# 完整测试流程
./scripts/test-deploy.sh

# 跳过构建（使用现有构建）
./scripts/test-deploy.sh --skip-build

# 跳过代码检查
./scripts/test-deploy.sh --skip-checks
```

## 服务器配置

### 权限配置

#### 服务器权限设置

```bash
# 创建部署用户
sudo useradd -m -s /bin/bash deploy

# 设置sudo权限（免密码）
echo "deploy ALL=(ALL) NOPASSWD: ALL" | sudo tee /etc/sudoers.d/deploy

# 创建部署目录
sudo mkdir -p /var/www/app
sudo chown -R deploy:deploy /var/www/app
sudo chmod -R 755 /var/www/app
```

#### SSH 密钥配置

```bash
# 在本地生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 复制公钥到服务器
ssh-copy-id <EMAIL>

# 或手动添加公钥
cat ~/.ssh/id_rsa.pub | ssh <EMAIL> "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys"
```

### Nginx 配置

#### 基本配置

```nginx
# /etc/nginx/sites-available/finsight-frontend
server {
    listen 80;
    server_name example.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 8090;
    server_name _;
    
    # SSL配置
    #ssl_certificate /path/to/certificate.crt;
    #ssl_certificate_key /path/to/private.key;
    
    # 应用根目录
    root /var/www/finsight/current;
    index index.html;
    
    # 静态文件缓存
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }
    
    # API代理（如果需要）
    #location /api/ {
    #    proxy_pass http://api-server;
    #    proxy_set_header Host $host;
    #    proxy_set_header X-Real-IP $remote_addr;
    #    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #    proxy_set_header X-Forwarded-Proto $scheme;
    #}
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "OK";
        add_header Content-Type text/plain;
    }
}
```

#### 启用配置

```bash
# 创建软链接
sudo ln -sf /etc/nginx/sites-available/finsight-frontend /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx
```

## 故障排除

### 常见部署错误

#### 1. 加密密钥相关错误

**错误信息**: "bad decrypt" 错误
**原因**: 密钥不正确或加密文件损坏
**解决方案**:
- 检查GitHub Secrets中的密钥是否正确
- 使用调试脚本验证本地解密: `./scripts/debug-encryption.sh`
- 重新加密文件

**错误信息**: 密钥未设置
**原因**: GitHub Secrets未正确配置
**解决方案**:
- 确认Secrets名称拼写正确（区分大小写）
- 确认Secrets值不包含额外的空格或换行符

#### 2. 权限错误

**错误信息**:
```
tar: Cannot open: Permission denied
tar: Cannot mkdir: Permission denied
```

**解决方案**:
```bash
# 确保目录权限正确
sudo chown -R $USER:$USER /var/www/app
sudo chmod -R 755 /var/www/app

# 检查SELinux状态（如果启用）
sestatus
sudo setsebool -P httpd_can_network_connect 1
```

#### 3. SSH连接失败

**错误信息**:
```
Permission denied (publickey)
```

**解决方案**:
```bash
# 检查SSH密钥权限
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub

# 测试SSH连接
ssh -v <EMAIL>

# 检查服务器SSH配置
sudo vi /etc/ssh/sshd_config
```

#### 4. 构建失败

**错误信息**:
```
npm ERR! code ELIFECYCLE
```

**解决方案**:
```bash
# 清理缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本
node --version  # 确保 >= 16.x
```

#### 5. 部署后页面空白

**可能原因**:
- 静态文件路径错误
- 缺少环境变量
- 路由配置问题

**解决方案**:
```bash
# 检查构建配置
cat build/asset-manifest.json

# 检查环境变量
printenv | grep REACT_APP

# 检查Nginx配置
sudo nginx -t
sudo systemctl status nginx
```

### 调试工具

使用项目提供的调试脚本：

```bash
# 检查环境和文件状态
./scripts/debug-encryption.sh check

# 测试基础加解密功能
./scripts/debug-encryption.sh test

# 测试真实文件解密
./scripts/debug-encryption.sh real development
```

### 监控和日志

#### 应用日志

```bash
# Nginx访问日志
sudo tail -f /var/log/nginx/access.log

# Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -u nginx -f
```

#### 性能监控

```bash
# 检查磁盘空间
df -h

# 检查内存使用
free -h

# 检查CPU使用
top

# 检查网络连接
netstat -tulpn | grep :80
```

## 性能优化

### 构建优化

```bash
# 启用生产优化
npm run build

# 分析包大小
npm install -g webpack-bundle-analyzer
npx webpack-bundle-analyzer build/static/js/*.js
```

### 服务器优化

```nginx
# 启用Gzip压缩
gzip on;
gzip_vary on;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/javascript
    application/xml+rss
    application/json;

# 启用HTTP/2
listen 443 ssl http2;
```

### CDN配置

```nginx
# 配置静态资源CDN
location /static/ {
    # 设置CDN头
    add_header X-Cache-Status $upstream_cache_status;
    expires 1y;
}
```

## 安全最佳实践

### 1. 密钥管理
- 使用强密钥（建议32位随机字符串）
- 定期轮换密钥
- 不要在代码中硬编码密钥

### 2. 访问控制
- 限制对GitHub Secrets的访问权限
- 定期审查有权访问的人员
- 使用专用部署用户
- 最小权限原则

### 3. 网络安全
- 使用HTTPS
- 配置防火墙
- 限制SSH访问

### 4. 数据保护
- 定期备份
- 加密敏感数据
- 日志监控

### 5. 版本控制
- 保留历史版本
- 快速回滚机制
- 变更记录

## 回滚流程

### 自动回滚

使用GitHub Actions工作流：

1. 进入仓库的 Actions 页面
2. 选择 "回滚部署" 工作流
3. 点击 "Run workflow"
4. 选择环境和回滚原因
5. 确认执行

### 手动回滚

```bash
# 使用脚本回滚
./scripts/deploy.sh rollback server.com deploy ~/.ssh/id_rsa /var/www/app

# 或者手动操作
ssh <EMAIL>
cd /var/www/app
sudo rm -f current
sudo ln -sf releases/previous current
sudo systemctl reload nginx
```

### 紧急回滚

如果生产环境部署出现问题，使用 `rollback.yml` 工作流进行快速回滚。

## 维护计划

### 定期任务

#### 每周
- 检查磁盘空间
- 清理旧版本
- 更新依赖

#### 每月
- 安全补丁更新
- 性能优化评估
- 备份验证

#### 每季度
- 灾难恢复测试
- 安全审计
- 文档更新

### 紧急响应

#### 服务中断
- 立即回滚到上一版本
- 通知相关团队
- 分析问题原因

#### 安全漏洞
- 立即修复并部署
- 检查受影响范围
- 更新安全措施

## 📊 部署状态监控

### 开发环境
- 标签格式：`dev-v{run_number}`
- 部署频率：每次 develop 分支推送
- 健康检查：基础文件完整性检查

### 生产环境
- 标签格式：`v{run_number}-prod`
- 部署频率：每次 main 分支推送
- 健康检查：完整的服务健康检查
- 发布记录：GitHub Releases

## 🔧 脚本依赖

工作流依赖以下脚本文件：

- `scripts/decrypt-env.sh` - 环境配置解密脚本
- `scripts/deploy.sh` - 部署脚本
- `scripts/debug-encryption.sh` - 加密调试脚本

确保这些脚本存在并且具有正确的执行权限。

## 📞 联系信息

如有 CI/CD 相关问题，请联系：
- **开发团队**: <EMAIL>
- **运维团队**: <EMAIL>
- **项目经理**: <EMAIL>

---

## 更新记录

- **2024-01-xx**: 将统一的 `ci_cd.yml` 拆分为 `develop.yml` 和 `main.yml`
- 增强了生产环境的安全检查
- 优化了开发环境的部署流程
- 添加了详细的错误处理和状态报告
- 整合了完整的 CI/CD 文档结构 