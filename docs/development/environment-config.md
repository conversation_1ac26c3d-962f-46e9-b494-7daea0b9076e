# 环境配置解密指南

## 概述

本项目使用加密的环境配置文件来保护敏感信息。在 CI/CD 流程中，这些配置文件会自动解密并应用到构建过程中。

## 文件结构

```
project/
├── .env.development.enc     # 开发环境加密配置
├── .env.production.enc      # 生产环境加密配置
├── scripts/
│   └── decrypt-env.sh       # 解密脚本
└── .github/workflows/
    ├── develop.yml          # 开发环境 CI/CD
    └── main.yml             # 生产环境 CI/CD
```

## 加密配置文件创建

### 1. 创建原始配置文件

```bash
# 开发环境配置
cat > .env.development << EOF
REACT_APP_ENV=development
REACT_APP_API_BASE_URL=https://api-dev.example.com
REACT_APP_DEBUG=true
REACT_APP_VERSION=1.0.0-dev
# 添加其他开发环境配置...
EOF

# 生产环境配置
cat > .env.production << EOF
REACT_APP_ENV=production
REACT_APP_API_BASE_URL=https://api.example.com
REACT_APP_DEBUG=false
REACT_APP_VERSION=1.0.0
# 添加其他生产环境配置...
EOF
```

### 2. 加密配置文件

```bash
# 生成强加密密钥（保存到 GitHub Secrets）
DEV_KEY=$(openssl rand -base64 32)
PROD_KEY=$(openssl rand -base64 32)

echo "开发环境密钥: $DEV_KEY"
echo "生产环境密钥: $PROD_KEY"

# 加密开发环境配置
openssl aes-256-cbc -a -pbkdf2 -iter 100000 \
  -in .env.development \
  -out .env.development.enc \
  -k "$DEV_KEY"

# 加密生产环境配置
openssl aes-256-cbc -a -pbkdf2 -iter 100000 \
  -in .env.production \
  -out .env.production.enc \
  -k "$PROD_KEY"

# 删除原始文件（安全考虑）
rm .env.development .env.production
```

### 3. 设置 GitHub Secrets

在 GitHub 仓库设置中添加以下 Secrets：

- `DEV_ENCRYPTION_KEY`: 开发环境解密密钥
- `PROD_ENCRYPTION_KEY`: 生产环境解密密钥

## 解密脚本使用

### 环境变量

脚本通过以下环境变量确定运行环境：

- `ENVIRONMENT`: 目标环境 (`development` 或 `production`)
- `DEV_ENCRYPTION_KEY`: 开发环境加密密钥
- `PROD_ENCRYPTION_KEY`: 生产环境加密密钥

### 本地测试

```bash
# 测试开发环境解密
export ENVIRONMENT=development
export DEV_ENCRYPTION_KEY="your-dev-key"
./scripts/decrypt-env.sh

# 测试生产环境解密
export ENVIRONMENT=production
export PROD_ENCRYPTION_KEY="your-prod-key"
./scripts/decrypt-env.sh
```

### 错误处理

解密脚本会在以下情况下失败并退出：

1. 加密密钥未设置或为空
2. 加密文件不存在或为空
3. 解密过程失败
4. 解密结果无效（不包含有效配置）

## CI/CD 集成

### 工作流配置

```yaml
- name: 解密环境配置文件
  env:
    DEV_ENCRYPTION_KEY: ${{ secrets.DEV_ENCRYPTION_KEY }}
    ENVIRONMENT: development
  run: |
    chmod +x ./scripts/decrypt-env.sh
    ./scripts/decrypt-env.sh
```

### 安全特性

1. **强制解密**: 不提供默认配置，解密失败时构建停止
2. **密钥验证**: 自动验证加密密钥的有效性
3. **文件验证**: 检查解密结果的完整性和有效性
4. **敏感信息保护**: 日志中隐藏敏感配置值

## 故障排除

### 常见错误

1. **加密密钥未设置**
   ```
   ERROR: development 环境的加密密钥未设置
   请设置对应的环境变量: DEV_ENCRYPTION_KEY
   ```
   
   解决方案: 在 GitHub Secrets 中添加对应的加密密钥

2. **加密文件不存在**
   ```
   ERROR: 未找到加密文件 .env.development.enc
   ```
   
   解决方案: 创建并提交加密配置文件

3. **解密失败**
   ```
   ERROR: 所有解密方法均失败
   ```
   
   解决方案: 检查加密密钥是否正确，重新生成加密文件

### 验证解密结果

```bash
# 检查解密后的文件
if [ -f ".env.local" ]; then
  echo "配置项数量: $(grep -c "^[^#].*=" .env.local)"
  echo "文件大小: $(wc -c < .env.local) 字节"
  
  # 显示配置结构（隐藏值）
  grep "^[^#]" .env.local | cut -d'=' -f1 | head -5
fi
```

## 安全建议

1. **定期轮换密钥**: 建议每季度更新加密密钥
2. **最小权限原则**: 仅给必要的用户访问 Secrets 的权限
3. **审计日志**: 定期检查 GitHub Actions 的运行日志
4. **备份恢复**: 保持加密文件的安全备份

## 相关文件

- `scripts/decrypt-env.sh`: 解密脚本
- `.github/workflows/develop.yml`: 开发环境流水线
- `.github/workflows/main.yml`: 生产环境流水线
- `docs/development/environment-config.md`: 本文档 