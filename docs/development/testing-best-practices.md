# 测试最佳实践指南

## 概述

本文档记录了项目中的测试最佳实践，特别是 Jest 测试框架的使用规范和常见问题解决方案。

## Mock 使用规范

### 1. 依赖模块 Mock

当测试中需要 mock 外部依赖时，确保正确导入和配置：

```typescript
// ✅ 正确的做法
import { logger } from '@/utils/logger';
import apiClient from '../api';

// Mock 外部依赖
jest.mock('@/utils/logger');
jest.mock('../api');

const mockedLogger = logger as jest.Mocked<typeof logger>;
const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;
```

### 2. 环境相关的 Mock

对于依赖环境变量的模块（如 logger），需要特别注意：

```typescript
// logger 只在开发环境输出日志
// 测试时需要 mock logger 而不是 console
expect(mockedLogger.warn).toHaveBeenCalledWith('消息', error);
```

### 3. 清理 Mock

在每个测试用例之间清理 mock 状态：

```typescript
describe('TestSuite', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
});
```

## 错误处理测试

### 1. 异步错误测试

```typescript
it('应该正确处理异步错误', async () => {
  const mockError = new Error('测试错误');
  mockedService.method.mockRejectedValue(mockError);

  await expect(serviceMethod()).rejects.toThrow(ApiError);
});
```

### 2. 日志记录测试

当方法捕获错误但不抛出时：

```typescript
it('应该记录错误但不抛出异常', async () => {
  const mockError = new Error('网络错误');
  mockedApiClient.post.mockRejectedValue(mockError);

  await expect(service.method()).resolves.toBeUndefined();
  expect(mockedLogger.warn).toHaveBeenCalledWith('错误消息:', mockError);
});
```

## 测试文件结构

### 1. 导入顺序

```typescript
// 1. 被测试的模块
import { ServiceClass } from '../service';

// 2. 外部依赖
import apiClient from '../api';
import { logger } from '@/utils/logger';

// 3. 类型定义
import { ApiError } from '@/types';

// 4. Mock 配置
jest.mock('../api');
jest.mock('@/utils/logger');
```

### 2. 测试用例组织

```typescript
describe('ServiceClass', () => {
  // 公共配置
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('methodName', () => {
    it('应该处理成功情况', () => {
      // 测试正常流程
    });

    it('应该处理错误情况', () => {
      // 测试错误处理
    });
  });
});
```

## 断言最佳实践

### 1. 精确断言

```typescript
// ✅ 精确匹配参数
expect(mockedFunction).toHaveBeenCalledWith(
  '/exact/path',
  { param: 'value' }
);

// ✅ 检查调用次数
expect(mockedFunction).toHaveBeenCalledTimes(1);
```

### 2. 异步断言

```typescript
// ✅ 异步操作断言
await expect(asyncMethod()).resolves.toEqual(expectedResult);
await expect(asyncMethod()).rejects.toThrow(ExpectedError);
```

## 常见问题和解决方案

### 1. Mock 没有被调用

**问题**: 测试失败，提示 mock 函数没有被调用

**原因**: 
- Mock 配置错误
- 环境变量导致代码分支不执行
- 导入路径不匹配

**解决方案**:
1. 检查 mock 配置是否正确
2. 确认环境变量设置
3. 验证导入路径一致性

### 2. 类型错误

**问题**: TypeScript 类型检查失败

**解决方案**:
```typescript
// 使用类型断言
const mockedFunction = jest.fn() as jest.MockedFunction<typeof originalFunction>;

// 或使用 jest.Mocked 工具类型
const mockedModule = module as jest.Mocked<typeof module>;
```

### 3. 异步测试超时

**问题**: 异步测试超时

**解决方案**:
```typescript
// 设置超时时间
it('异步测试', async () => {
  // 测试代码
}, 10000); // 10秒超时

// 或在 beforeEach 中设置
beforeEach(() => {
  jest.setTimeout(10000);
});
```

## 覆盖率要求

- 语句覆盖率: ≥ 80%
- 分支覆盖率: ≥ 75%
- 函数覆盖率: ≥ 80%
- 行覆盖率: ≥ 80%

### 当前覆盖率状态

- authService.ts: 100% 覆盖率 ✅
- phoneValidator.ts: 100% 覆盖率 ✅
- SmsCodeInput 组件: 90%+ 覆盖率 ✅

## 测试命令

```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- authService.test.ts

# 生成覆盖率报告
npm test -- --coverage --watchAll=false

# 详细输出
npm test -- --verbose

# 静默运行（CI/CD）
npm test -- --watchAll=false --silent
```

## 持续集成

测试在部署检查中的执行顺序：

```bash
npm run deploy:check
# 1. validate:cicd     - 验证 CI/CD 配置
# 2. type-check        - TypeScript 类型检查
# 3. lint:check        - ESLint 代码风格检查
# 4. test              - 运行所有测试
```

## 最后更新

- 修复了 authService.test.ts 中的 logger mock 问题
- 确保所有测试通过，覆盖率达标
- 更新了 Mock 使用规范

---

*文档最后更新时间: 2024-01-01* 