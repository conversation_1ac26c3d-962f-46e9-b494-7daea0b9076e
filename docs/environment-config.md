# 环境配置管理文档

## 概述

本项目采用环境变量配置管理方案，支持多环境部署和配置管理。通过不同的环境配置文件，可以轻松在开发、测试、生产环境之间切换。

## 文件结构

```
├── .env.example          # 环境变量示例文件
├── .env.development      # 开发环境配置
├── .env.production       # 生产环境配置
├── src/
│   ├── config/
│   │   ├── env.ts        # 环境配置模块
│   │   └── index.ts      # 配置统一导出
│   ├── hooks/
│   │   └── useConfig.ts  # 配置相关 Hooks
│   └── types/
│       └── env.d.ts      # 环境变量类型定义
```

## 环境配置文件

### .env.example

包含所有可用的环境变量及其说明，作为配置参考模板。

### .env.development

开发环境配置，包含：
- 本地 API 地址
- 启用调试模式和 Mock 数据
- 开发专用的第三方服务配置

### .env.production

生产环境配置，包含：
- 生产 API 地址
- 禁用调试功能
- 生产环境的安全配置

## 配置分类

### 1. 应用基础配置
- `REACT_APP_NAME`: 应用名称
- `REACT_APP_VERSION`: 应用版本
- `REACT_APP_DESCRIPTION`: 应用描述
- `REACT_APP_ENV`: 当前环境

### 2. API 配置
- `REACT_APP_API_BASE_URL`: API 基础地址
- `REACT_APP_API_VERSION`: API 版本
- `REACT_APP_API_TIMEOUT`: 请求超时时间

### 3. 认证配置
- `REACT_APP_JWT_SECRET_KEY`: JWT 密钥
- `REACT_APP_JWT_EXPIRES_IN`: JWT 过期时间
- `REACT_APP_REFRESH_TOKEN_EXPIRES_IN`: 刷新令牌过期时间

### 4. 功能开关
- `REACT_APP_ENABLE_MOCK_DATA`: 启用 Mock 数据
- `REACT_APP_ENABLE_DEBUG_MODE`: 启用调试模式
- `REACT_APP_ENABLE_HOT_RELOAD`: 启用热重载

### 5. 第三方服务
- `REACT_APP_GOOGLE_ANALYTICS_ID`: Google Analytics ID
- `REACT_APP_SENTRY_DSN`: Sentry DSN

### 6. 上传配置
- `REACT_APP_MAX_FILE_SIZE`: 最大文件大小（字节）
- `REACT_APP_ALLOWED_FILE_TYPES`: 允许的文件类型（逗号分隔）

### 7. 缓存配置
- `REACT_APP_CACHE_TTL`: 缓存存活时间（毫秒）
- `REACT_APP_LOCAL_STORAGE_PREFIX`: 本地存储前缀

### 8. WebSocket 配置
- `REACT_APP_WS_URL`: WebSocket 地址
- `REACT_APP_WS_RECONNECT_INTERVAL`: 重连间隔（毫秒）

### 9. 主题配置
- `REACT_APP_DEFAULT_THEME`: 默认主题
- `REACT_APP_PRIMARY_COLOR`: 主色调
- `REACT_APP_SUCCESS_COLOR`: 成功色
- `REACT_APP_WARNING_COLOR`: 警告色
- `REACT_APP_ERROR_COLOR`: 错误色

### 10. 分页配置
- `REACT_APP_DEFAULT_PAGE_SIZE`: 默认分页大小
- `REACT_APP_MAX_PAGE_SIZE`: 最大分页大小

### 11. 地图配置
- `REACT_APP_MAP_API_KEY`: 地图 API 密钥
- `REACT_APP_MAP_DEFAULT_CENTER_LAT`: 默认中心纬度
- `REACT_APP_MAP_DEFAULT_CENTER_LNG`: 默认中心经度

### 12. CDN 配置
- `REACT_APP_CDN_BASE_URL`: CDN 基础地址
- `REACT_APP_STATIC_ASSETS_URL`: 静态资源地址

## 使用方式

### 1. 基础配置使用

```tsx
import { config, useConfig } from '@/config';

// 直接使用配置对象
console.log(config.api.baseUrl);

// 在组件中使用 Hook
const MyComponent = () => {
  const { config, environment, isDevelopment } = useConfig();
  
  return (
    <div>
      <h1>{config.name}</h1>
      <p>当前环境: {environment}</p>
    </div>
  );
};
```

### 2. 环境检查

```tsx
import { useEnvironment } from '@/config';

const MyComponent = () => {
  const { isDev, isProd, isTest } = useEnvironment();
  
  if (isDev()) {
    return <DevComponent />;
  }
  
  if (isProd()) {
    return <ProdComponent />;
  }
  
  return <DefaultComponent />;
};
```

### 3. 功能开关

```tsx
import { useFeatureFlags } from '@/config';

const MyComponent = () => {
  const { isMockDataEnabled, isDebugModeEnabled } = useFeatureFlags();
  
  return (
    <div>
      {isMockDataEnabled && <MockDataIndicator />}
      {isDebugModeEnabled && <DebugPanel />}
    </div>
  );
};
```

### 4. API 配置

```tsx
import { useApiConfig } from '@/config';

const useUserApi = () => {
  const { buildApiUrl, buildVersionedApiUrl } = useApiConfig();
  
  const getUserList = () => {
    const url = buildApiUrl('/users');
    return fetch(url);
  };
  
  const getUserById = (id: string) => {
    const url = buildVersionedApiUrl(`/users/${id}`);
    return fetch(url);
  };
  
  return { getUserList, getUserById };
};
```

### 5. 主题配置

```tsx
import { useThemeConfig } from '@/config';

const ThemedButton = () => {
  const { primaryColor, getColor } = useThemeConfig();
  
  return (
    <button
      style={{
        backgroundColor: primaryColor,
        borderColor: getColor('primaryColor'),
      }}
    >
      主题按钮
    </button>
  );
};
```

## 环境切换

### 开发环境
```bash
# 复制开发环境配置
cp .env.development .env.local

# 启动开发服务器
npm start
```

### 生产环境
```bash
# 复制生产环境配置
cp .env.production .env.local

# 构建生产版本
npm run build
```

### 自定义环境
```bash
# 创建自定义环境配置
cp .env.example .env.custom

# 编辑配置文件
vim .env.custom

# 使用自定义配置
cp .env.custom .env.local
```

## 配置验证

项目启动时会自动验证必需的配置项：

```tsx
import { validateConfig } from '@/config';

try {
  validateConfig();
  console.log('✅ 配置验证通过');
} catch (error) {
  console.error('❌ 配置验证失败:', error);
}
```

## 最佳实践

### 1. 安全性
- 敏感信息（如密钥）不要提交到版本控制
- 生产环境使用强密码和密钥
- 定期更新安全相关配置

### 2. 可维护性
- 使用有意义的配置名称
- 添加详细的配置注释
- 保持配置文件结构一致

### 3. 开发效率
- 使用 TypeScript 类型检查
- 利用功能开关进行特性控制
- 在开发环境启用调试功能

### 4. 部署配置
- 使用 CI/CD 自动化部署
- 根据环境自动选择配置文件
- 验证配置完整性

## 故障排除

### 常见问题

1. **配置加载失败**
   - 检查环境变量名称是否正确
   - 确认 `.env` 文件位置和格式
   - 检查 TypeScript 类型定义

2. **环境判断错误**
   - 确认 `REACT_APP_ENV` 变量设置
   - 检查环境变量值格式
   - 验证配置导入路径

3. **API 请求失败**
   - 检查 API 基础地址配置
   - 确认网络连接和跨域设置
   - 验证 API 版本配置

### 调试方法

1. **开发环境调试**
```tsx
// 启用调试模式
REACT_APP_ENABLE_DEBUG_MODE=true

// 查看配置信息
import { printConfig } from '@/config';
printConfig();
```

2. **配置检查**
```tsx
import { config } from '@/config';
console.log('当前配置:', config);
```

3. **环境变量检查**
```bash
# 查看所有环境变量
printenv | grep REACT_APP
```

## 扩展配置

### 添加新配置项

1. **更新环境变量文件**
```bash
# 在 .env.example 中添加新配置
REACT_APP_NEW_FEATURE_ENABLED=false
```

2. **更新类型定义**
```tsx
// src/types/env.d.ts
interface ProcessEnv {
  readonly REACT_APP_NEW_FEATURE_ENABLED?: string;
}
```

3. **更新配置对象**
```tsx
// src/config/env.ts
export const config = {
  features: {
    newFeatureEnabled: getBooleanEnvVar('REACT_APP_NEW_FEATURE_ENABLED', false),
  },
};
```

4. **添加 Hook 支持**
```tsx
// src/hooks/useConfig.ts
export const useFeatureFlags = () => {
  return {
    isNewFeatureEnabled: isFeatureEnabled('newFeatureEnabled'),
  };
};
```

### 配置分组管理

对于复杂的配置，可以按功能模块分组：

```tsx
// src/config/modules/payment.ts
export const paymentConfig = {
  provider: getEnvVar('REACT_APP_PAYMENT_PROVIDER'),
  apiKey: getEnvVar('REACT_APP_PAYMENT_API_KEY'),
  webhookSecret: getEnvVar('REACT_APP_PAYMENT_WEBHOOK_SECRET'),
};
```

## 参考资料

- [Create React App 环境变量文档](https://create-react-app.dev/docs/adding-custom-environment-variables/)
- [TypeScript 环境变量类型定义](https://www.typescriptlang.org/docs/handbook/declaration-merging.html)
- [React Hooks 最佳实践](https://reactjs.org/docs/hooks-rules.html) 