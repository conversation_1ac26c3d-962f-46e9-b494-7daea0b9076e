# FinSight Frontend

Financial Insight 前端应用 - 基于 React + TypeScript + Ant Design 构建的现代化金融数据分析平台。

## 🚀 快速开始

### 环境要求
- Node.js 18.x+
- npm 8.x+

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run start:dev    # 使用开发环境配置启动
npm start           # 默认启动
```

### 构建项目
```bash
npm run build:prod  # 生产环境构建
npm run build       # 默认构建
```

## 🔧 开发工具

### 代码质量检查
```bash
npm run lint:check      # 检查代码规范
npm run lint           # 修复代码规范问题
npm run prettier:check # 检查代码格式
npm run prettier       # 格式化代码
npm run type-check     # TypeScript 类型检查
```

### 安全检查
```bash
npm run security:check  # 检查安全漏洞
npm run security:fix   # 自动修复安全漏洞
npm run security:report # 生成安全报告
```

### 测试
```bash
npm test               # 运行测试
npm run test:coverage  # 运行测试并生成覆盖率报告
```

### 环境管理
```bash
npm run env:dev        # 切换到开发环境
npm run env:prod       # 切换到生产环境
npm run env:status     # 查看当前环境状态
npm run env:cleanup    # 清理环境备份文件
```

## 🎯 CI/CD 自动化部署

本项目使用分环境的 GitHub Actions CI/CD 流程，针对不同环境提供定制化的部署策略：

### 工作流配置
- **develop.yml**: 开发环境专用工作流
- **main.yml**: 生产环境专用工作流
- **rollback.yml**: 紧急回滚工作流

### 分支策略
- **develop 分支**: 触发开发环境工作流，快速部署和验证
- **main 分支**: 触发生产环境工作流，严格安全检查和部署

### 开发环境流程 (develop.yml)
1. 代码质量检查（ESLint、Prettier、TypeScript）
2. 单元测试执行
3. 开发环境构建
4. 基础健康检查
5. 自动部署到开发服务器
6. 创建开发标签 (`dev-v{run_number}`)

### 生产环境流程 (main.yml)
1. 完整代码质量检查
2. **智能安全扫描** (自动修复 + 高危漏洞阻断)
3. 生产环境构建
4. **构建质量检查** (大小、调试信息清理)
5. **最终安全检查**
6. **创建部署备份**
7. 生产服务器部署
8. **完整健康检查**
9. 创建 GitHub Release (`v{run_number}-prod`)

### 安全保障机制
- **自动漏洞修复**: 使用智能脚本自动修复已知安全漏洞
- **高危漏洞阻断**: 发现高危或严重漏洞时自动阻止部署
- **敏感信息检测**: 多层次扫描源码中的敏感信息泄露
- **依赖强制更新**: 使用 `overrides` 确保安全版本的依赖包

### 验证配置
```bash
./scripts/validate-deployment.sh  # 验证部署配置
./scripts/test-deploy.sh          # 本地测试部署
```

详细配置说明请参考：[CI/CD 工作流配置说明](./docs/development/ci-cd-workflows.md)

## 📁 项目结构

```
src/
├── assets/          # 静态资源
├── components/      # 可复用组件
│   ├── common/      # 通用组件
│   └── ui/          # UI 组件
├── features/        # 功能模块
├── hooks/           # 自定义 Hooks
├── layouts/         # 布局组件
├── pages/           # 页面组件
├── routes/          # 路由配置
├── services/        # API 服务
├── store/           # 状态管理
├── styles/          # 样式文件
├── types/           # 类型定义
└── utils/           # 工具函数
```

## 🛠️ 技术栈

- **React 18** - 用户界面库
- **TypeScript** - 类型安全的 JavaScript
- **Ant Design 5** - 企业级 UI 组件库
- **Redux Toolkit** - 状态管理
- **React Router 6** - 路由管理
- **Styled Components** - CSS-in-JS 样式解决方案
- **React Query** - 数据获取和缓存
- **Axios** - HTTP 客户端

## 📖 开发指南

### 代码规范
- 遵循 ESLint + Prettier 配置
- 使用 TypeScript 严格模式
- 组件采用函数式 + Hooks 模式
- 遵循 Git 提交规范

### 组件开发
- 使用 TypeScript 定义 Props 接口
- 实现响应式设计
- 添加适当的错误边界
- 编写单元测试

### 状态管理
- 本地状态使用 useState/useReducer
- 全局状态使用 Redux Toolkit
- 服务端状态使用 React Query
- 表单状态使用专用 Hook

## 🚀 部署

### 本地测试部署
在实际部署前，建议先进行本地测试：
```bash
# 完整测试流程
./scripts/test-deploy.sh

# 跳过构建使用现有文件
./scripts/test-deploy.sh --skip-build
```

### 手动部署
使用部署脚本进行手动部署：
```bash
# 部署到开发环境
./scripts/deploy.sh deploy \
  dev.example.com \
  deploy \
  ~/.ssh/id_rsa \
  /var/www/dev \
  development

# 部署到生产环境  
./scripts/deploy.sh deploy \
  prod.example.com \
  deploy \
  ~/.ssh/id_rsa \
  /var/www/prod \
  production
```

### 回滚部署
如需回滚到上一个版本：
```bash
# 使用脚本回滚
./scripts/deploy.sh rollback \
  server.example.com \
  deploy \
  ~/.ssh/id_rsa \
  /var/www/app

# 或使用GitHub Actions
# 进入Actions页面 → 选择"回滚部署"工作流 → 手动触发
```

### GitHub Secrets 配置
在 GitHub 仓库设置中配置以下 Secrets：

**环境文件加密密钥**:
- `DEV_ENCRYPTION_KEY`: 开发环境配置文件解密密钥
- `PROD_ENCRYPTION_KEY`: 生产环境配置文件解密密钥

**开发环境**:
- `DEV_HOST`: 开发服务器地址
- `DEV_USERNAME`: 部署用户名
- `DEV_SSH_KEY`: SSH 私钥
- `DEV_DEPLOY_PATH`: 部署路径

**生产环境**:
- `PROD_HOST`: 生产服务器地址
- `PROD_USERNAME`: 部署用户名
- `PROD_SSH_KEY`: SSH 私钥
- `PROD_DEPLOY_PATH`: 部署路径

### 环境配置文件加密
本项目使用加密的环境配置文件来保护敏感信息：

```bash
# 重新生成加密密钥和文件
./scripts/regenerate-encryption.sh

# 调试加密问题
./scripts/debug-encryption.sh check

# 手动加密（如有需要）
export DEV_ENCRYPTION_KEY="your-dev-key"
export PROD_ENCRYPTION_KEY="your-prod-key"
./job_before_push.sh
```

详细配置请参考：[GitHub Secrets 设置指南](./docs/development/GITHUB_SECRETS_SETUP.md)

### 部署目录结构
```
/var/www/app/
├── releases/           # 历史版本目录
│   ├── 20241218_143022 # 版本时间戳目录
│   └── 20241218_151045
├── shared/             # 共享资源目录
├── current -> releases/20241218_151045  # 当前版本软链接
└── previous -> releases/20241218_143022 # 前一版本（回滚用）
```

### 常见部署问题解决

**权限错误**:
```bash
sudo chown -R $USER:$USER /var/www/app
sudo chmod -R 755 /var/www/app
```

**SSH连接失败**:
```bash
chmod 600 ~/.ssh/id_rsa
ssh-copy-id <EMAIL>
```

详细部署文档请参考：[部署指南](./docs/deployment.md)

## 📄 文档

- [部署指南](./docs/deployment.md)
- [CI/CD 配置指南](./docs/development/CICD_SETUP.md)
- [API 文档](./docs/api/)
- [组件库文档](./docs/UI/)
- [用户指南](./docs/user/)

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📝 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

> 💡 **提示**: 首次部署前请确保已完成服务器环境配置和 GitHub Secrets 设置。 