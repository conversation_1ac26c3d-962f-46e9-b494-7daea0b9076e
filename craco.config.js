const path = require('path');

module.exports = {
  webpack: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@components': path.resolve(__dirname, 'src/components'),
      '@pages': path.resolve(__dirname, 'src/pages'),
      '@routes': path.resolve(__dirname, 'src/routes'),
      '@layouts': path.resolve(__dirname, 'src/layouts'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@constants': path.resolve(__dirname, 'src/constants'),
      '@hooks': path.resolve(__dirname, 'src/hooks'),
      '@store': path.resolve(__dirname, 'src/store'),
      '@services': path.resolve(__dirname, 'src/services'),
      '@types': path.resolve(__dirname, 'src/types'),
      '@styles': path.resolve(__dirname, 'src/styles'),
      '@assets': path.resolve(__dirname, 'src/assets'),
      '@features': path.resolve(__dirname, 'src/features'),
    },
  },
  jest: {
    configure: {
      moduleNameMapper: {
        '^@/(.*)$': '<rootDir>/src/$1',
        '^@components/(.*)$': '<rootDir>/src/components/$1',
        '^@pages/(.*)$': '<rootDir>/src/pages/$1',
        '^@services/(.*)$': '<rootDir>/src/services/$1',
        '^@utils/(.*)$': '<rootDir>/src/utils/$1',
        '^@hooks/(.*)$': '<rootDir>/src/hooks/$1',
        '^@store/(.*)$': '<rootDir>/src/store/$1',
        '^@constants/(.*)$': '<rootDir>/src/constants/$1',
        '^@styles/(.*)$': '<rootDir>/src/styles/$1',
        '^@layouts/(.*)$': '<rootDir>/src/layouts/$1',
        '^@routes/(.*)$': '<rootDir>/src/routes/$1',
        '^@types/(.*)$': '<rootDir>/src/types/$1',
        '^@assets/(.*)$': '<rootDir>/src/assets/$1',
        '^@features/(.*)$': '<rootDir>/src/features/$1',
        '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
        '^axios$': '<rootDir>/__mocks__/axios.js',
      },
              transformIgnorePatterns: [
          'node_modules/(?!axios)',
        ],
      testEnvironment: 'jsdom',
      setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
    },
  },
}; 